#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث قاعدة البيانات - إضافة الأعمدة المفقودة
Database Migration - Add missing columns
"""

import asyncio
import aiomysql
import sys

# إعدادات قاعدة البيانات
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '',
    'db': 'matjer_bot',
    'charset': 'utf8mb4'
}

async def migrate_database():
    """تحديث قاعدة البيانات"""
    print("🔧 بدء تحديث قاعدة البيانات...")
    
    try:
        conn = await aiomysql.connect(**DB_CONFIG)
        async with conn.cursor() as cur:
            
            # التحقق من وجود عمود payment_method
            await cur.execute("""
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = %s AND TABLE_NAME = 'orders' AND COLUMN_NAME = 'payment_method'
            """, (DB_CONFIG['db'],))
            
            payment_method_exists = await cur.fetchone()
            
            if not payment_method_exists:
                print("➕ إضافة عمود payment_method...")
                await cur.execute("""
                    ALTER TABLE orders 
                    ADD COLUMN payment_method VARCHAR(50) DEFAULT 'cash' AFTER status
                """)
                print("✅ تم إضافة عمود payment_method")
            else:
                print("✅ عمود payment_method موجود بالفعل")
            
            # التحقق من وجود عمود delivery_info
            await cur.execute("""
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = %s AND TABLE_NAME = 'orders' AND COLUMN_NAME = 'delivery_info'
            """, (DB_CONFIG['db'],))
            
            delivery_info_exists = await cur.fetchone()
            
            if not delivery_info_exists:
                print("➕ إضافة عمود delivery_info...")
                await cur.execute("""
                    ALTER TABLE orders 
                    ADD COLUMN delivery_info JSON AFTER payment_method
                """)
                print("✅ تم إضافة عمود delivery_info")
            else:
                print("✅ عمود delivery_info موجود بالفعل")
            
            # التحقق من وجود عمود notes
            await cur.execute("""
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = %s AND TABLE_NAME = 'orders' AND COLUMN_NAME = 'notes'
            """, (DB_CONFIG['db'],))
            
            notes_exists = await cur.fetchone()
            
            if not notes_exists:
                print("➕ إضافة عمود notes...")
                await cur.execute("""
                    ALTER TABLE orders 
                    ADD COLUMN notes TEXT AFTER delivery_info
                """)
                print("✅ تم إضافة عمود notes")
            else:
                print("✅ عمود notes موجود بالفعل")
            
            # تحديث قيم status إذا لزم الأمر
            await cur.execute("""
                ALTER TABLE orders 
                MODIFY COLUMN status ENUM('pending', 'accepted', 'rejected', 'shipping', 'delivered') DEFAULT 'pending'
            """)
            print("✅ تم تحديث قيم status")
            
            print("🎉 تم تحديث قاعدة البيانات بنجاح!")
            
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث قاعدة البيانات: {e}")
        return False

async def main():
    """الدالة الرئيسية"""
    success = await migrate_database()
    
    if success:
        print("\n🚀 يمكنك الآن تشغيل الاختبار:")
        print("python test_system.py")
    else:
        print("\n❌ فشل في تحديث قاعدة البيانات")
        sys.exit(1)

if __name__ == '__main__':
    asyncio.run(main())
