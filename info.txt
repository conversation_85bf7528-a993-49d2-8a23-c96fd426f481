# تثبيت المكتبات المطلوبة
pip install python-telegram-bot

# تشغيل البوت
python bot.py




6️⃣ الإعدادات النهائية:

ضع توكن البوت: في متغير TOKEN
ضع رابط التطبيق: في متغير WEB_APP_URL
أضف معرفات المديرين: في قائمة ADMIN_IDS
اختبر البوت: أرسل /start للبوت

📱 الميزات الجديدة:

✅ استقبال الطلبات في الشات
✅ إشعارات فورية للمديرين
✅ قبول/رفض الطلبات
✅ متابعة حالة الطلبات
✅ لوحة تحكم إدارية
✅ إحصائيات شاملة
✅ التواصل المباشر مع العملاء

البوت الآن جاهز للعمل الحقيقي! 🚀