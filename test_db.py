#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import aiomysql
from datetime import datetime

# إعداد قاعدة البيانات
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '',
    'db': 'matjer_bot',
    'autocommit': True
}

async def test_database():
    """اختبار الاتصال بقاعدة البيانات والتحقق من الجداول"""
    try:
        print("🔄 اختبار الاتصال بقاعدة البيانات...")
        conn = await aiomysql.connect(**DB_CONFIG)
        print("✅ تم الاتصال بقاعدة البيانات بنجاح!")
        
        async with conn.cursor(aiomysql.DictCursor) as cur:
            # التحقق من جدول المستخدمين
            print("\n📊 التحقق من جدول المستخدمين...")
            await cur.execute("SELECT COUNT(*) as count FROM users")
            users_count = (await cur.fetchone())['count']
            print(f"👥 عدد المستخدمين: {users_count}")
            
            # عرض آخر 5 مستخدمين
            await cur.execute("SELECT * FROM users ORDER BY join_date DESC LIMIT 5")
            users = await cur.fetchall()
            print("👤 آخر المستخدمين:")
            for user in users:
                print(f"  - ID: {user['id']}, الاسم: {user['name']}, تاريخ الانضمام: {user['join_date']}")
            
            # التحقق من جدول الطلبات
            print("\n📦 التحقق من جدول الطلبات...")
            await cur.execute("SELECT COUNT(*) as count FROM orders")
            orders_count = (await cur.fetchone())['count']
            print(f"🛒 عدد الطلبات: {orders_count}")
            
            # عرض آخر 5 طلبات
            await cur.execute("""
                SELECT o.*, u.name as customer_name 
                FROM orders o 
                LEFT JOIN users u ON o.user_id = u.id 
                ORDER BY o.created_at DESC 
                LIMIT 5
            """)
            orders = await cur.fetchall()
            print("📋 آخر الطلبات:")
            for order in orders:
                print(f"  - طلب #{order['id']}: {order['customer_name']} - {order['total']} جنيه - {order['status']}")
            
            # التحقق من جدول المنتجات
            print("\n🛍️ التحقق من جدول المنتجات...")
            await cur.execute("SELECT COUNT(*) as count FROM products")
            products_count = (await cur.fetchone())['count']
            print(f"📦 عدد المنتجات: {products_count}")
            
            # عرض المنتجات
            await cur.execute("SELECT * FROM products LIMIT 5")
            products = await cur.fetchall()
            print("🛒 المنتجات المتاحة:")
            for product in products:
                print(f"  - {product['name']}: {product['price']} جنيه (المخزون: {product['stock_quantity']})")
        
        conn.close()
        print("\n✅ اكتمل اختبار قاعدة البيانات بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")

async def test_user_insertion():
    """اختبار إدراج مستخدم جديد"""
    try:
        print("\n🧪 اختبار إدراج مستخدم جديد...")
        conn = await aiomysql.connect(**DB_CONFIG)
        
        # إدراج مستخدم تجريبي
        test_user_id = 999999999
        async with conn.cursor() as cur:
            await cur.execute('''
                INSERT IGNORE INTO users (id, name, username, join_date)
                VALUES (%s, %s, %s, %s)
            ''', (test_user_id, 'مستخدم تجريبي', 'test_user', datetime.now()))
            
            # التحقق من الإدراج
            await cur.execute('SELECT * FROM users WHERE id = %s', (test_user_id,))
            user = await cur.fetchone()
            
            if user:
                print(f"✅ تم إدراج المستخدم التجريبي: {user}")
            else:
                print("❌ فشل في إدراج المستخدم التجريبي")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إدراج المستخدم: {e}")

if __name__ == "__main__":
    print("🚀 بدء اختبار قاعدة البيانات...")
    asyncio.run(test_database())
    asyncio.run(test_user_insertion())
