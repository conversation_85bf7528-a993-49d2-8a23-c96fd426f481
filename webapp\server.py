#!/usr/bin/env python3
"""
خادم ويب بسيط لتشغيل تطبيق التسوق
"""

import http.server
import socketserver
import os
import sys
from urllib.parse import urlparse, parse_qs
import json
import threading
import webbrowser

# إعدادات الخادم
PORT = 8000
DIRECTORY = os.path.dirname(os.path.abspath(__file__))

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=DIRECTORY, **kwargs)
    
    def end_headers(self):
        # إضافة headers للسماح بـ CORS
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_GET(self):
        # توجيه الطلبات إلى index.html
        if self.path == '/' or self.path == '':
            self.path = '/index.html'
        
        return super().do_GET()
    
    def do_POST(self):
        # معالجة طلبات POST للـ API
        if self.path.startswith('/api/'):
            self.handle_api_request()
        else:
            self.send_response(404)
            self.end_headers()

    def handle_api_request(self):
        """معالجة طلبات API"""
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length)

        try:
            data = json.loads(post_data.decode('utf-8'))

            # معالجة البيانات حسب المسار
            if self.path == '/api/products':
                response = self.get_products()
            elif self.path == '/api/categories':
                response = self.get_categories()
            else:
                response = {'success': False, 'error': 'مسار API غير مدعوم'}

            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

        except Exception as e:
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            error_response = {'success': False, 'error': f'خطأ في الخادم: {str(e)}'}
            self.wfile.write(json.dumps(error_response, ensure_ascii=False).encode('utf-8'))
    
    def get_products(self):
        """إرجاع قائمة المنتجات"""
        return {
            'success': True,
            'products': [
                {
                    'id': 1,
                    'name': 'هاتف ذكي',
                    'description': 'هاتف ذكي بمواصفات عالية وكاميرا متطورة',
                    'price': 2500,
                    'category_id': 1,
                    'stock_quantity': 50,
                    'image': '📱',
                    'is_available': True
                },
                {
                    'id': 2,
                    'name': 'لابتوب',
                    'description': 'لابتوب للألعاب والعمل بمعالج قوي',
                    'price': 15000,
                    'category_id': 1,
                    'stock_quantity': 20,
                    'image': '💻',
                    'is_available': True
                },
                {
                    'id': 3,
                    'name': 'قميص قطني',
                    'description': 'قميص قطني مريح ومناسب لجميع المناسبات',
                    'price': 150,
                    'category_id': 2,
                    'stock_quantity': 100,
                    'image': '👕',
                    'is_available': True
                },
                {
                    'id': 4,
                    'name': 'بنطلون جينز',
                    'description': 'بنطلون جينز عالي الجودة ومقاوم للتمزق',
                    'price': 300,
                    'category_id': 2,
                    'stock_quantity': 75,
                    'image': '👖',
                    'is_available': True
                },
                {
                    'id': 5,
                    'name': 'مصباح LED',
                    'description': 'مصباح LED موفر للطاقة وإضاءة ممتازة',
                    'price': 80,
                    'category_id': 3,
                    'stock_quantity': 200,
                    'image': '💡',
                    'is_available': True
                },
                {
                    'id': 6,
                    'name': 'كتاب البرمجة',
                    'description': 'كتاب تعلم البرمجة للمبتدئين خطوة بخطوة',
                    'price': 120,
                    'category_id': 4,
                    'stock_quantity': 30,
                    'image': '📖',
                    'is_available': True
                },
                {
                    'id': 7,
                    'name': 'كرة قدم',
                    'description': 'كرة قدم احترافية مناسبة للملاعب العشبية',
                    'price': 200,
                    'category_id': 5,
                    'stock_quantity': 40,
                    'image': '⚽',
                    'is_available': True
                }
            ]
        }
    
    def get_categories(self):
        """إرجاع قائمة الفئات"""
        return {
            'success': True,
            'categories': [
                {'id': 1, 'name': 'الإلكترونيات', 'icon': '📱', 'description': 'أجهزة إلكترونية متنوعة'},
                {'id': 2, 'name': 'الملابس', 'icon': '👕', 'description': 'ملابس رجالية ونسائية'},
                {'id': 3, 'name': 'المنزل والحديقة', 'icon': '🏠', 'description': 'أدوات منزلية ومستلزمات الحديقة'},
                {'id': 4, 'name': 'الكتب', 'icon': '📚', 'description': 'كتب متنوعة في جميع المجالات'},
                {'id': 5, 'name': 'الرياضة', 'icon': '⚽', 'description': 'معدات رياضية ولياقة بدنية'}
            ]
        }
    
    def log_message(self, format, *args):
        # تخصيص رسائل السجل
        print(f"[{self.address_string()}] {format % args}")

def start_server():
    """بدء تشغيل الخادم"""
    try:
        with socketserver.TCPServer(("", PORT), CustomHTTPRequestHandler) as httpd:
            print(f"🚀 خادم الويب يعمل على المنفذ {PORT}")
            print(f"🌐 رابط التطبيق: http://localhost:{PORT}")
            print(f"📁 مجلد الملفات: {DIRECTORY}")
            print("=" * 50)
            print("للإيقاف اضغط Ctrl+C")
            print("=" * 50)
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الخادم")
        sys.exit(0)
    except OSError as e:
        if e.errno == 98:  # Address already in use
            print(f"❌ المنفذ {PORT} مستخدم بالفعل!")
            print("جرب منفذ آخر أو أوقف الخادم الآخر")
        else:
            print(f"❌ خطأ في بدء الخادم: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        sys.exit(1)

def open_browser():
    """فتح المتصفح تلقائياً"""
    import time
    time.sleep(1)  # انتظار ثانية لبدء الخادم
    try:
        webbrowser.open(f'http://localhost:{PORT}')
        print(f"🌐 تم فتح المتصفح على: http://localhost:{PORT}")
    except Exception as e:
        print(f"⚠️ لم يتم فتح المتصفح تلقائياً: {e}")

if __name__ == "__main__":
    print("🛍️ خادم تطبيق التسوق")
    print("=" * 30)
    
    # التحقق من وجود الملفات المطلوبة
    required_files = ['index.html', 'script.js']
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(os.path.join(DIRECTORY, file)):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
        print("تأكد من وجود جميع ملفات التطبيق في نفس المجلد")
        sys.exit(1)
    
    # بدء فتح المتصفح في خيط منفصل
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    # بدء الخادم
    start_server()
