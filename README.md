# 🛍️ متجر تليجرام - نظام التجارة الإلكترونية

نظام متكامل للتجارة الإلكترونية عبر تليجرام يتيح للعملاء تصفح المنتجات وإضافتها للسلة وإتمام الطلبات بسهولة.

## ✨ المميزات

### للعملاء:
- 🛒 تصفح المنتجات حسب الفئات
- 🛍️ إضافة المنتجات للسلة
- 💳 طرق دفع متعددة (نقدي، بطاقة ائتمان، تحويل بنكي، محفظة إلكترونية)
- 📦 تتبع حالة الطلبات
- 📱 واجهة ويب تفاعلية داخل تليجرام

### للمديرين:
- 📊 لوحة تحكم شاملة
- 📦 إدارة المنتجات والفئات
- 📋 إدارة الطلبات وتحديث حالاتها
- 📈 إحصائيات المبيعات
- 🔔 إشعارات فورية للطلبات الجديدة

## 🏗️ البنية التقنية

### المكونات الرئيسية:
- **bot.py**: البوت الرئيسي لتليجرام
- **webapp/**: واجهة الويب التفاعلية
  - `index.html`: الواجهة الرئيسية
  - `script.js`: منطق التطبيق
  - `server.py`: خادم الويب المحلي
  - `api.py`: واجهة برمجة التطبيقات

### قاعدة البيانات:
- **MySQL** لحفظ البيانات
- جداول: `categories`, `products`, `orders`, `order_items`

## 🚀 التثبيت والتشغيل

### 1. متطلبات النظام:
```bash
pip install python-telegram-bot aiomysql
```

### 2. إعداد قاعدة البيانات:
- إنشاء قاعدة بيانات MySQL باسم `matjer_bot`
- تحديث بيانات الاتصال في `bot.py`

### 3. إعداد البوت:
- إنشاء بوت جديد عبر @BotFather
- الحصول على Token
- تحديث `TOKEN` في `bot.py`
- إضافة معرف المدير في `ADMIN_IDS`

### 4. تشغيل النظام:

#### تشغيل البوت:
```bash
python bot.py
```

#### تشغيل خادم الويب:
```bash
cd webapp
python server.py
```

## 📱 كيفية الاستخدام

### للعملاء:
1. ابدأ محادثة مع البوت `/start`
2. اضغط على "🛍️ فتح المتجر" لتصفح المنتجات
3. اختر المنتجات وأضفها للسلة
4. أكمل بيانات التوصيل وطريقة الدفع
5. أرسل الطلب وتابع حالته

### للمديرين:
1. استخدم `/admin` للوصول للوحة التحكم
2. إدارة المنتجات والفئات
3. متابعة الطلبات الجديدة
4. تحديث حالات الطلبات

## 🔧 الإعدادات

### في `bot.py`:
```python
TOKEN = "YOUR_BOT_TOKEN"
ADMIN_IDS = [123456789]  # معرفات المديرين
ADMIN_CHAT_ID = 123456789  # معرف المدير الرئيسي

# إعدادات قاعدة البيانات
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'your_password',
    'db': 'matjer_bot'
}
```

## 📊 حالات الطلبات

- **⏳ pending**: قيد المراجعة
- **✅ accepted**: مقبول
- **❌ rejected**: مرفوض
- **🚚 shipping**: جاري التوصيل
- **📦 delivered**: تم التوصيل

## 💳 طرق الدفع المدعومة

- 💵 الدفع عند الاستلام
- 💳 بطاقة ائتمان
- 🏦 تحويل بنكي
- 📱 محفظة إلكترونية

## 🛡️ الأمان

- التحقق من صلاحيات المديرين
- تشفير البيانات الحساسة
- التحقق من صحة البيانات المدخلة
- حماية من SQL Injection

## 🔄 التحديثات المستقبلية

- [ ] إضافة نظام التقييمات
- [ ] تكامل مع بوابات الدفع
- [ ] تقارير مبيعات متقدمة
- [ ] نظام الخصومات والعروض
- [ ] دعم اللغات المتعددة

## 📞 الدعم الفني

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- إنشاء Issue في GitHub
- التواصل مع فريق التطوير

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

---

**تم تطويره بـ ❤️ لخدمة المجتمع العربي**
