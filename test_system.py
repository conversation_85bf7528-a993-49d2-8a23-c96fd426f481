#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لنظام متجر تليجرام
Test Suite for Telegram E-commerce Bot System
"""

import asyncio
import aiomysql
import json
from datetime import datetime
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# إعدادات قاعدة البيانات (نفس الإعدادات في bot.py)
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '',
    'db': 'matjer_bot',
    'charset': 'utf8mb4'
}

class SystemTester:
    """فئة اختبار النظام"""
    
    def __init__(self):
        self.test_results = []
        self.passed_tests = 0
        self.failed_tests = 0
    
    async def get_db_connection(self):
        """الاتصال بقاعدة البيانات"""
        try:
            return await aiomysql.connect(**DB_CONFIG)
        except Exception as e:
            print(f"❌ فشل الاتصال بقاعدة البيانات: {e}")
            return None
    
    def log_test(self, test_name, passed, message=""):
        """تسجيل نتيجة الاختبار"""
        status = "✅ نجح" if passed else "❌ فشل"
        result = f"{status} {test_name}"
        if message:
            result += f" - {message}"
        
        self.test_results.append(result)
        if passed:
            self.passed_tests += 1
        else:
            self.failed_tests += 1
        
        print(result)
    
    async def test_database_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        print("\n🔍 اختبار الاتصال بقاعدة البيانات...")
        
        conn = await self.get_db_connection()
        if conn:
            self.log_test("اتصال قاعدة البيانات", True)
            conn.close()
        else:
            self.log_test("اتصال قاعدة البيانات", False, "فشل الاتصال")
    
    async def test_database_tables(self):
        """اختبار وجود الجداول المطلوبة"""
        print("\n🔍 اختبار جداول قاعدة البيانات...")
        
        required_tables = ['categories', 'products', 'users', 'orders', 'order_items']
        conn = await self.get_db_connection()
        
        if not conn:
            self.log_test("جداول قاعدة البيانات", False, "لا يمكن الاتصال بقاعدة البيانات")
            return
        
        try:
            async with conn.cursor() as cur:
                await cur.execute("SHOW TABLES")
                existing_tables = [table[0] for table in await cur.fetchall()]
                
                for table in required_tables:
                    if table in existing_tables:
                        self.log_test(f"جدول {table}", True)
                    else:
                        self.log_test(f"جدول {table}", False, "الجدول غير موجود")
        
        except Exception as e:
            self.log_test("جداول قاعدة البيانات", False, str(e))
        finally:
            conn.close()
    
    async def test_sample_data(self):
        """اختبار البيانات التجريبية"""
        print("\n🔍 اختبار البيانات التجريبية...")
        
        conn = await self.get_db_connection()
        if not conn:
            self.log_test("البيانات التجريبية", False, "لا يمكن الاتصال بقاعدة البيانات")
            return
        
        try:
            async with conn.cursor(aiomysql.DictCursor) as cur:
                # اختبار الفئات
                await cur.execute("SELECT COUNT(*) as count FROM categories")
                categories_count = (await cur.fetchone())['count']
                self.log_test("فئات المنتجات", categories_count > 0, f"عدد الفئات: {categories_count}")
                
                # اختبار المنتجات
                await cur.execute("SELECT COUNT(*) as count FROM products WHERE is_active = TRUE")
                products_count = (await cur.fetchone())['count']
                self.log_test("المنتجات النشطة", products_count > 0, f"عدد المنتجات: {products_count}")
                
                # اختبار المنتجات مع الأسعار
                await cur.execute("SELECT COUNT(*) as count FROM products WHERE price > 0")
                priced_products = (await cur.fetchone())['count']
                self.log_test("المنتجات بأسعار صحيحة", priced_products > 0, f"المنتجات المسعرة: {priced_products}")
        
        except Exception as e:
            self.log_test("البيانات التجريبية", False, str(e))
        finally:
            conn.close()
    
    async def test_order_system(self):
        """اختبار نظام الطلبات"""
        print("\n🔍 اختبار نظام الطلبات...")
        
        conn = await self.get_db_connection()
        if not conn:
            self.log_test("نظام الطلبات", False, "لا يمكن الاتصال بقاعدة البيانات")
            return
        
        try:
            async with conn.cursor(aiomysql.DictCursor) as cur:
                # إنشاء مستخدم تجريبي أولاً
                test_user_id = 123456789
                await cur.execute('''
                    INSERT IGNORE INTO users (id)
                    VALUES (%s)
                ''', (test_user_id,))

                # إنشاء طلب تجريبي
                test_order_data = {
                    'items': [{'id': 1, 'name': 'منتج تجريبي', 'price': 100, 'quantity': 2}],
                    'total': 200,
                    'payment_method': 'cash',
                    'delivery_info': {
                        'name': 'عميل تجريبي',
                        'phone': '01234567890',
                        'address': 'عنوان تجريبي'
                    }
                }
                
                # إدراج الطلب
                await cur.execute('''
                    INSERT INTO orders (user_id, items, total, status, payment_method, created_at, delivery_info)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                ''', (
                    test_user_id,
                    json.dumps(test_order_data['items'], ensure_ascii=False),
                    test_order_data['total'],
                    'pending',
                    test_order_data['payment_method'],
                    datetime.now(),
                    json.dumps(test_order_data['delivery_info'], ensure_ascii=False)
                ))
                
                order_id = cur.lastrowid
                self.log_test("إنشاء طلب تجريبي", True, f"معرف الطلب: {order_id}")
                
                # اختبار استرجاع الطلب
                await cur.execute("SELECT * FROM orders WHERE id = %s", (order_id,))
                order = await cur.fetchone()
                
                if order:
                    self.log_test("استرجاع الطلب", True)
                    
                    # اختبار تحديث حالة الطلب
                    await cur.execute("UPDATE orders SET status = %s WHERE id = %s", ('accepted', order_id))
                    self.log_test("تحديث حالة الطلب", True)
                else:
                    self.log_test("استرجاع الطلب", False)
                
                # حذف الطلب التجريبي
                await cur.execute("DELETE FROM orders WHERE id = %s", (order_id,))
                self.log_test("حذف الطلب التجريبي", True)

                # حذف المستخدم التجريبي
                await cur.execute("DELETE FROM users WHERE id = %s", (test_user_id,))
                self.log_test("حذف المستخدم التجريبي", True)
        
        except Exception as e:
            self.log_test("نظام الطلبات", False, str(e))
        finally:
            conn.close()
    
    async def test_web_app_files(self):
        """اختبار ملفات تطبيق الويب"""
        print("\n🔍 اختبار ملفات تطبيق الويب...")
        
        webapp_files = [
            'webapp/index.html',
            'webapp/script.js',
            'webapp/server.py',
            'webapp/api.py'
        ]
        
        for file_path in webapp_files:
            if os.path.exists(file_path):
                # اختبار حجم الملف
                file_size = os.path.getsize(file_path)
                if file_size > 0:
                    self.log_test(f"ملف {file_path}", True, f"الحجم: {file_size} بايت")
                else:
                    self.log_test(f"ملف {file_path}", False, "الملف فارغ")
            else:
                self.log_test(f"ملف {file_path}", False, "الملف غير موجود")
    
    async def test_bot_configuration(self):
        """اختبار إعدادات البوت"""
        print("\n🔍 اختبار إعدادات البوت...")
        
        try:
            # استيراد إعدادات البوت
            import bot
            
            # اختبار وجود TOKEN
            if hasattr(bot, 'TOKEN') and bot.TOKEN and bot.TOKEN != "YOUR_BOT_TOKEN":
                self.log_test("رمز البوت (TOKEN)", True)
            else:
                self.log_test("رمز البوت (TOKEN)", False, "لم يتم تعيين رمز صحيح")
            
            # اختبار معرفات المديرين
            if hasattr(bot, 'ADMIN_IDS') and bot.ADMIN_IDS:
                self.log_test("معرفات المديرين", True, f"عدد المديرين: {len(bot.ADMIN_IDS)}")
            else:
                self.log_test("معرفات المديرين", False, "لم يتم تعيين مديرين")
            
            # اختبار رابط تطبيق الويب
            if hasattr(bot, 'WEB_APP_URL') and bot.WEB_APP_URL:
                self.log_test("رابط تطبيق الويب", True)
            else:
                self.log_test("رابط تطبيق الويب", False, "لم يتم تعيين رابط")
        
        except ImportError as e:
            self.log_test("استيراد إعدادات البوت", False, str(e))
    
    async def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🚀 بدء اختبار النظام الشامل...")
        print("=" * 50)
        
        # تشغيل الاختبارات
        await self.test_database_connection()
        await self.test_database_tables()
        await self.test_sample_data()
        await self.test_order_system()
        await self.test_web_app_files()
        await self.test_bot_configuration()
        
        # عرض النتائج النهائية
        print("\n" + "=" * 50)
        print("📊 نتائج الاختبار النهائية:")
        print(f"✅ اختبارات نجحت: {self.passed_tests}")
        print(f"❌ اختبارات فشلت: {self.failed_tests}")
        print(f"📈 معدل النجاح: {(self.passed_tests / (self.passed_tests + self.failed_tests) * 100):.1f}%")
        
        if self.failed_tests == 0:
            print("\n🎉 تهانينا! جميع الاختبارات نجحت!")
            print("النظام جاهز للاستخدام.")
        else:
            print(f"\n⚠️ يوجد {self.failed_tests} اختبار فشل.")
            print("يرجى مراجعة الأخطاء وإصلاحها قبل تشغيل النظام.")
        
        return self.failed_tests == 0

async def main():
    """الدالة الرئيسية"""
    tester = SystemTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🚀 يمكنك الآن تشغيل النظام:")
        print("1. تشغيل البوت: python bot.py")
        print("2. تشغيل خادم الويب: cd webapp && python server.py")
    
    return success

if __name__ == '__main__':
    asyncio.run(main())
