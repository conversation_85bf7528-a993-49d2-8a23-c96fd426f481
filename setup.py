#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد سريع لنظام متجر تليجرام
Quick Setup for Telegram E-commerce Bot System
"""

import asyncio
import aiomysql
import json
import os
import sys
from datetime import datetime

# إعدادات قاعدة البيانات
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '',
    'db': 'matjer_bot',
    'charset': 'utf8mb4'
}

class SystemSetup:
    """فئة إعداد النظام"""
    
    def __init__(self):
        self.setup_steps = []
    
    def log_step(self, step_name, success, message=""):
        """تسجيل خطوة الإعداد"""
        status = "✅" if success else "❌"
        result = f"{status} {step_name}"
        if message:
            result += f" - {message}"
        
        self.setup_steps.append(result)
        print(result)
        return success
    
    async def get_db_connection(self):
        """الاتصال بقاعدة البيانات"""
        try:
            return await aiomysql.connect(**DB_CONFIG)
        except Exception as e:
            print(f"❌ فشل الاتصال بقاعدة البيانات: {e}")
            return None
    
    async def create_database(self):
        """إنشاء قاعدة البيانات"""
        print("\n🔧 إنشاء قاعدة البيانات...")
        
        try:
            # الاتصال بدون تحديد قاعدة بيانات
            config_without_db = DB_CONFIG.copy()
            del config_without_db['db']
            
            conn = await aiomysql.connect(**config_without_db)
            async with conn.cursor() as cur:
                await cur.execute(f"CREATE DATABASE IF NOT EXISTS {DB_CONFIG['db']} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                self.log_step("إنشاء قاعدة البيانات", True)
            conn.close()
            return True
        except Exception as e:
            self.log_step("إنشاء قاعدة البيانات", False, str(e))
            return False
    
    async def create_tables(self):
        """إنشاء الجداول"""
        print("\n🔧 إنشاء الجداول...")
        
        conn = await self.get_db_connection()
        if not conn:
            return False
        
        try:
            async with conn.cursor() as cur:
                # جدول المستخدمين
                await cur.execute('''
                    CREATE TABLE IF NOT EXISTS users (
                        id BIGINT PRIMARY KEY,
                        first_name VARCHAR(255),
                        last_name VARCHAR(255),
                        username VARCHAR(255),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                self.log_step("جدول المستخدمين", True)
                
                # جدول الفئات
                await cur.execute('''
                    CREATE TABLE IF NOT EXISTS categories (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        name VARCHAR(255) NOT NULL,
                        description TEXT,
                        is_active BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                self.log_step("جدول الفئات", True)
                
                # جدول المنتجات
                await cur.execute('''
                    CREATE TABLE IF NOT EXISTS products (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        name VARCHAR(255) NOT NULL,
                        description TEXT,
                        price DECIMAL(10,2) NOT NULL,
                        category_id INT,
                        image_url VARCHAR(500),
                        stock_quantity INT DEFAULT 0,
                        is_active BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (category_id) REFERENCES categories(id)
                    )
                ''')
                self.log_step("جدول المنتجات", True)
                
                # جدول الطلبات
                await cur.execute('''
                    CREATE TABLE IF NOT EXISTS orders (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        user_id BIGINT NOT NULL,
                        items JSON,
                        total DECIMAL(10,2) NOT NULL,
                        status ENUM('pending', 'accepted', 'rejected', 'shipping', 'delivered') DEFAULT 'pending',
                        payment_method VARCHAR(50) DEFAULT 'cash',
                        delivery_info JSON,
                        notes TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users(id)
                    )
                ''')
                self.log_step("جدول الطلبات", True)
                
                # جدول تفاصيل الطلبات
                await cur.execute('''
                    CREATE TABLE IF NOT EXISTS order_items (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        order_id INT NOT NULL,
                        product_id INT NOT NULL,
                        quantity INT NOT NULL,
                        price DECIMAL(10,2) NOT NULL,
                        FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
                        FOREIGN KEY (product_id) REFERENCES products(id)
                    )
                ''')
                self.log_step("جدول تفاصيل الطلبات", True)
                
            return True
        except Exception as e:
            self.log_step("إنشاء الجداول", False, str(e))
            return False
        finally:
            conn.close()
    
    async def insert_sample_data(self):
        """إدراج بيانات تجريبية"""
        print("\n🔧 إدراج البيانات التجريبية...")
        
        conn = await self.get_db_connection()
        if not conn:
            return False
        
        try:
            async with conn.cursor() as cur:
                # إدراج فئات تجريبية
                categories = [
                    ('إلكترونيات', 'أجهزة إلكترونية ومعدات تقنية'),
                    ('ملابس', 'ملابس رجالية ونسائية وأطفال'),
                    ('منزل ومطبخ', 'أدوات منزلية ومطبخية'),
                    ('كتب', 'كتب ومجلات تعليمية وثقافية'),
                    ('رياضة', 'معدات رياضية ولياقة بدنية')
                ]
                
                for name, desc in categories:
                    await cur.execute(
                        "INSERT IGNORE INTO categories (name, description) VALUES (%s, %s)",
                        (name, desc)
                    )
                
                self.log_step("إدراج الفئات التجريبية", True, f"{len(categories)} فئة")
                
                # إدراج منتجات تجريبية
                products = [
                    ('هاتف ذكي', 'هاتف ذكي بمواصفات عالية', 2500.00, 1, 50),
                    ('لابتوب', 'جهاز كمبيوتر محمول للعمل والألعاب', 15000.00, 1, 20),
                    ('قميص قطني', 'قميص قطني مريح للاستخدام اليومي', 150.00, 2, 100),
                    ('بنطلون جينز', 'بنطلون جينز عالي الجودة', 300.00, 2, 75),
                    ('طقم أواني طبخ', 'طقم أواني طبخ من الستانلس ستيل', 800.00, 3, 30),
                    ('كتاب البرمجة', 'كتاب تعليم البرمجة للمبتدئين', 120.00, 4, 200),
                    ('دمبل رياضي', 'دمبل للتمارين الرياضية', 250.00, 5, 40)
                ]
                
                for name, desc, price, cat_id, stock in products:
                    await cur.execute('''
                        INSERT IGNORE INTO products (name, description, price, category_id, stock_quantity)
                        VALUES (%s, %s, %s, %s, %s)
                    ''', (name, desc, price, cat_id, stock))
                
                self.log_step("إدراج المنتجات التجريبية", True, f"{len(products)} منتج")
                
            return True
        except Exception as e:
            self.log_step("إدراج البيانات التجريبية", False, str(e))
            return False
        finally:
            conn.close()
    
    def create_config_file(self):
        """إنشاء ملف الإعدادات"""
        print("\n🔧 إنشاء ملف الإعدادات...")
        
        config_content = '''# إعدادات البوت - يرجى تحديث القيم التالية

# رمز البوت من @BotFather
TOKEN = "YOUR_BOT_TOKEN_HERE"

# معرفات المديرين (أضف معرفك هنا)
ADMIN_IDS = [123456789]  # استبدل بمعرفك الحقيقي

# معرف المدير الرئيسي للإشعارات
ADMIN_CHAT_ID = 123456789  # استبدل بمعرفك الحقيقي

# رابط تطبيق الويب
WEB_APP_URL = "http://localhost:8000"

# إعدادات قاعدة البيانات
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '',  # أضف كلمة مرور قاعدة البيانات إذا لزم الأمر
    'db': 'matjer_bot',
    'charset': 'utf8mb4'
}

print("✅ تم تحميل الإعدادات")
'''
        
        try:
            with open('config.py', 'w', encoding='utf-8') as f:
                f.write(config_content)
            self.log_step("إنشاء ملف الإعدادات", True, "config.py")
            return True
        except Exception as e:
            self.log_step("إنشاء ملف الإعدادات", False, str(e))
            return False
    
    def create_requirements_file(self):
        """إنشاء ملف المتطلبات"""
        print("\n🔧 إنشاء ملف المتطلبات...")
        
        requirements = '''# متطلبات نظام متجر تليجرام
python-telegram-bot==20.7
aiomysql==0.2.0
asyncio
'''
        
        try:
            with open('requirements.txt', 'w', encoding='utf-8') as f:
                f.write(requirements)
            self.log_step("إنشاء ملف المتطلبات", True, "requirements.txt")
            return True
        except Exception as e:
            self.log_step("إنشاء ملف المتطلبات", False, str(e))
            return False
    
    async def run_setup(self):
        """تشغيل الإعداد الكامل"""
        print("🚀 بدء إعداد نظام متجر تليجرام...")
        print("=" * 50)
        
        success = True
        
        # خطوات الإعداد
        success &= await self.create_database()
        success &= await self.create_tables()
        success &= await self.insert_sample_data()
        success &= self.create_config_file()
        success &= self.create_requirements_file()
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 تم إعداد النظام بنجاح!")
            print("\n📋 الخطوات التالية:")
            print("1. قم بتحديث ملف config.py بالمعلومات الصحيحة")
            print("2. تثبيت المتطلبات: pip install -r requirements.txt")
            print("3. تشغيل الاختبار: python test_system.py")
            print("4. تشغيل البوت: python bot.py")
            print("5. تشغيل خادم الويب: cd webapp && python server.py")
        else:
            print("❌ فشل في إعداد النظام!")
            print("يرجى مراجعة الأخطاء وإعادة المحاولة.")
        
        return success

async def main():
    """الدالة الرئيسية"""
    setup = SystemSetup()
    await setup.run_setup()

if __name__ == '__main__':
    asyncio.run(main())
