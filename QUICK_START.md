# 🚀 دليل التشغيل السريع - متجر تليجرام

## 📋 المتطلبات الأساسية

### 1. البرامج المطلوبة:
- **Python 3.8+** 
- **MySQL Server** أو **MariaDB**
- **Git** (اختياري)

### 2. حساب تليجرام:
- إنشاء بوت جديد عبر [@BotFather](https://t.me/BotFather)
- الحصول على **Bot Token**

## ⚡ التثبيت السريع

### الخطوة 1: تحضير البيئة
```bash
# تثبيت المكتبات المطلوبة
pip install python-telegram-bot==20.7 aiomysql==0.2.0

# أو استخدام ملف المتطلبات (بعد تشغيل setup.py)
pip install -r requirements.txt
```

### الخطوة 2: إعداد قاعدة البيانات
```bash
# تشغيل إعداد النظام التلقائي
python setup.py
```

### الخطوة 3: تحديث الإعدادات
افتح ملف `config.py` وحدث المعلومات التالية:

```python
# رمز البوت من @BotFather
TOKEN = "1234567890:ABCdefGHIjklMNOpqrsTUVwxyz"

# معرف المدير (معرفك في تليجرام)
ADMIN_IDS = [123456789]  # استبدل بمعرفك الحقيقي
ADMIN_CHAT_ID = 123456789

# إعدادات قاعدة البيانات (إذا لزم الأمر)
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'your_password',  # أضف كلمة المرور إذا لزم
    'db': 'matjer_bot'
}
```

### الخطوة 4: اختبار النظام
```bash
# تشغيل اختبار شامل للنظام
python test_system.py
```

### الخطوة 5: تشغيل النظام
```bash
# تشغيل البوت (في terminal منفصل)
python bot.py

# تشغيل خادم الويب (في terminal آخر)
cd webapp
python server.py
```

## 🔧 كيفية الحصول على معرف تليجرام

### طريقة 1: استخدام @userinfobot
1. ابحث عن [@userinfobot](https://t.me/userinfobot)
2. ابدأ محادثة معه
3. سيرسل لك معرفك الرقمي

### طريقة 2: استخدام @RawDataBot
1. ابحث عن [@RawDataBot](https://t.me/RawDataBot)
2. أرسل له أي رسالة
3. ستجد معرفك في `"id": 123456789`

## 📱 كيفية الاستخدام

### للعملاء:
1. ابدأ محادثة مع البوت `/start`
2. اضغط "🛍️ فتح المتجر"
3. تصفح المنتجات وأضفها للسلة
4. أكمل بيانات التوصيل
5. أرسل الطلب

### للمديرين:
1. استخدم الأمر `/admin`
2. إدارة المنتجات والطلبات
3. متابعة الإحصائيات

## 🛠️ إضافة منتجات جديدة

### طريقة 1: عبر الأمر
```
/add_product اسم_المنتج السعر الفئة الوصف
```

مثال:
```
/add_product هاتف_ذكي 2500 1 هاتف بمواصفات عالية
```

### طريقة 2: عبر لوحة الإدارة
1. `/admin` → "📦 إدارة المنتجات"
2. اتبع التعليمات

## 🔍 استكشاف الأخطاء

### مشكلة: البوت لا يستجيب
- **الحل**: تأكد من صحة `TOKEN` في `config.py`
- تأكد من تشغيل `python bot.py`

### مشكلة: خطأ في قاعدة البيانات
- **الحل**: تأكد من تشغيل MySQL
- تحقق من إعدادات `DB_CONFIG`
- شغل `python setup.py` مرة أخرى

### مشكلة: تطبيق الويب لا يعمل
- **الحل**: تأكد من تشغيل `python webapp/server.py`
- تحقق من أن المنفذ 8000 متاح

### مشكلة: "غير مصرح لك بالوصول"
- **الحل**: أضف معرفك إلى `ADMIN_IDS` في `config.py`

## 📊 الميزات المتاحة

### ✅ للعملاء:
- [x] تصفح المنتجات حسب الفئات
- [x] إضافة المنتجات للسلة
- [x] طرق دفع متعددة
- [x] تتبع حالة الطلبات
- [x] واجهة ويب تفاعلية

### ✅ للمديرين:
- [x] لوحة تحكم شاملة
- [x] إدارة المنتجات والفئات
- [x] إدارة الطلبات
- [x] إحصائيات المبيعات
- [x] إشعارات فورية

## 🔄 التحديثات المستقبلية

- [ ] نظام التقييمات
- [ ] تكامل بوابات الدفع
- [ ] تقارير متقدمة
- [ ] نظام الخصومات
- [ ] دعم اللغات المتعددة

## 📞 الدعم الفني

إذا واجهت أي مشاكل:

1. **تشغيل الاختبار**: `python test_system.py`
2. **مراجعة الأخطاء**: تحقق من رسائل الخطأ في Terminal
3. **إعادة الإعداد**: شغل `python setup.py` مرة أخرى

## 🎯 نصائح مهمة

### الأمان:
- لا تشارك `TOKEN` مع أحد
- استخدم كلمة مرور قوية لقاعدة البيانات
- قم بعمل نسخ احتياطية دورية

### الأداء:
- راقب استخدام الذاكرة
- نظف قاعدة البيانات دورياً
- استخدم فهرسة مناسبة للجداول

### التطوير:
- اقرأ التوثيق في `README.md`
- استخدم `test_system.py` قبل أي تحديث
- احتفظ بنسخ احتياطية من الكود

---

**🎉 مبروك! نظامك جاهز للعمل**

للمساعدة الإضافية، راجع ملف `README.md` للتوثيق الشامل.
