# -*- coding: utf-8 -*-
"""
ملف إعدادات نظام متجر تليجرام
Configuration file for Telegram E-commerce Bot System

يرجى تحديث القيم التالية قبل تشغيل النظام
Please update the following values before running the system
"""

# ================================
# إعدادات البوت الأساسية
# Basic Bot Settings
# ================================

# رمز البوت من @BotFather
# Bot Token from @BotFather
TOKEN = "YOUR_BOT_TOKEN_HERE"

# معرفات المديرين (قائمة بمعرفات المديرين)
# Admin IDs (List of admin user IDs)
ADMIN_IDS = [
    123456789,  # استبدل بمعرفك الحقيقي / Replace with your actual ID
    # 987654321,  # يمكن إضافة مديرين آخرين / Can add more admins
]

# معرف المدير الرئيسي للإشعارات
# Main admin ID for notifications
ADMIN_CHAT_ID = 123456789  # استبدل بمعرفك الحقيقي / Replace with your actual ID

# ================================
# إعدادات تطبيق الويب
# Web App Settings
# ================================

# رابط تطبيق الويب (يجب أن يكون متاحاً عبر HTTPS في الإنتاج)
# Web App URL (Must be HTTPS in production)
WEB_APP_URL = "http://localhost:8000"

# منفذ خادم الويب المحلي
# Local web server port
WEB_SERVER_PORT = 8000

# ================================
# إعدادات قاعدة البيانات
# Database Settings
# ================================

DB_CONFIG = {
    'host': 'localhost',        # عنوان خادم قاعدة البيانات
    'user': 'root',            # اسم المستخدم
    'password': '',            # كلمة المرور (أضف كلمة المرور إذا لزم الأمر)
    'db': 'matjer_bot',        # اسم قاعدة البيانات
    'charset': 'utf8mb4',      # ترميز الأحرف
    'autocommit': True         # تأكيد التغييرات تلقائياً
}

# ================================
# إعدادات النظام
# System Settings
# ================================

# عدد المنتجات في الصفحة الواحدة
PRODUCTS_PER_PAGE = 10

# عدد الطلبات في الصفحة الواحدة
ORDERS_PER_PAGE = 5

# الحد الأدنى لتنبيه المخزون
LOW_STOCK_THRESHOLD = 5

# ================================
# إعدادات الرسائل
# Message Settings
# ================================

# رسالة الترحيب
WELCOME_MESSAGE = """
🛍️ مرحباً بك في متجرنا الإلكتروني!

يمكنك تصفح منتجاتنا وإضافتها للسلة وإتمام الطلب بسهولة.

اضغط على الزر أدناه لفتح المتجر:
"""

# رسالة المساعدة
HELP_MESSAGE = """
📋 كيفية استخدام المتجر:

🛍️ للعملاء:
• اضغط "فتح المتجر" لتصفح المنتجات
• أضف المنتجات للسلة
• أكمل بيانات التوصيل
• اختر طريقة الدفع
• أرسل الطلب

⚙️ للمديرين:
• استخدم /admin للوصول للوحة التحكم
• إدارة المنتجات والطلبات
• متابعة الإحصائيات

📞 للدعم الفني:
تواصل مع الإدارة عبر الأزرار المتاحة
"""

# ================================
# إعدادات طرق الدفع
# Payment Methods Settings
# ================================

PAYMENT_METHODS = {
    'cash': {
        'name': 'الدفع عند الاستلام',
        'emoji': '💵',
        'enabled': True
    },
    'card': {
        'name': 'بطاقة ائتمان',
        'emoji': '💳',
        'enabled': True
    },
    'bank': {
        'name': 'تحويل بنكي',
        'emoji': '🏦',
        'enabled': True
    },
    'wallet': {
        'name': 'محفظة إلكترونية',
        'emoji': '📱',
        'enabled': True
    }
}

# ================================
# إعدادات حالات الطلبات
# Order Status Settings
# ================================

ORDER_STATUSES = {
    'pending': {
        'name': 'قيد المراجعة',
        'emoji': '⏳',
        'color': 'yellow'
    },
    'accepted': {
        'name': 'مقبول',
        'emoji': '✅',
        'color': 'green'
    },
    'rejected': {
        'name': 'مرفوض',
        'emoji': '❌',
        'color': 'red'
    },
    'shipping': {
        'name': 'جاري التوصيل',
        'emoji': '🚚',
        'color': 'blue'
    },
    'delivered': {
        'name': 'تم التوصيل',
        'emoji': '📦',
        'color': 'green'
    }
}

# ================================
# إعدادات التسجيل
# Logging Settings
# ================================

# مستوى التسجيل (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL = "INFO"

# ملف السجل
LOG_FILE = "bot.log"

# تنسيق رسائل السجل
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# ================================
# إعدادات الأمان
# Security Settings
# ================================

# الحد الأقصى لحجم الطلب (بالجنيه)
MAX_ORDER_AMOUNT = 50000

# الحد الأقصى لعدد المنتجات في الطلب الواحد
MAX_ITEMS_PER_ORDER = 50

# مهلة انتظار الاستجابة (بالثواني)
REQUEST_TIMEOUT = 30

# ================================
# إعدادات التطوير
# Development Settings
# ================================

# وضع التطوير (True للتطوير، False للإنتاج)
DEBUG_MODE = True

# عرض رسائل التصحيح
VERBOSE_LOGGING = True

# إعادة تحميل البوت عند تغيير الكود
AUTO_RELOAD = False

# ================================
# التحقق من الإعدادات
# Settings Validation
# ================================

def validate_config():
    """التحقق من صحة الإعدادات"""
    errors = []
    
    if TOKEN == "YOUR_BOT_TOKEN_HERE":
        errors.append("❌ يجب تحديث TOKEN برمز البوت الصحيح")
    
    if 123456789 in ADMIN_IDS:
        errors.append("⚠️ يجب تحديث ADMIN_IDS بمعرفات المديرين الحقيقية")
    
    if ADMIN_CHAT_ID == 123456789:
        errors.append("⚠️ يجب تحديث ADMIN_CHAT_ID بمعرف المدير الحقيقي")
    
    if not DB_CONFIG.get('host'):
        errors.append("❌ يجب تحديد عنوان خادم قاعدة البيانات")
    
    return errors

# تشغيل التحقق عند استيراد الملف
if __name__ != '__main__':
    validation_errors = validate_config()
    if validation_errors:
        print("🔧 تحذيرات الإعدادات:")
        for error in validation_errors:
            print(f"  {error}")
        print("\n📝 يرجى تحديث ملف config.py قبل تشغيل النظام")
    else:
        print("✅ تم تحميل الإعدادات بنجاح")

# ================================
# تصدير الإعدادات
# Export Settings
# ================================

__all__ = [
    'TOKEN', 'ADMIN_IDS', 'ADMIN_CHAT_ID', 'WEB_APP_URL', 'WEB_SERVER_PORT',
    'DB_CONFIG', 'PRODUCTS_PER_PAGE', 'ORDERS_PER_PAGE', 'LOW_STOCK_THRESHOLD',
    'WELCOME_MESSAGE', 'HELP_MESSAGE', 'PAYMENT_METHODS', 'ORDER_STATUSES',
    'LOG_LEVEL', 'LOG_FILE', 'LOG_FORMAT', 'MAX_ORDER_AMOUNT', 'MAX_ITEMS_PER_ORDER',
    'REQUEST_TIMEOUT', 'DEBUG_MODE', 'VERBOSE_LOGGING', 'AUTO_RELOAD', 'validate_config'
]
