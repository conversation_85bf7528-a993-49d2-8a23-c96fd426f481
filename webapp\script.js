// إعداد Telegram Web App
let tg = window.Telegram.WebApp;
tg.expand();

// إعداد API
const API_BASE_URL = window.location.hostname === 'localhost' ?
    'http://localhost:8000' :
    null; // استخدام البيانات المحلية للاستضافة الخارجية

// متغيرات عامة
let products = [];
let categories = [];
let cart = [];
let selectedPaymentMethod = 'cash';

// تحميل البيانات عند بدء التطبيق
document.addEventListener('DOMContentLoaded', function() {
    loadCategories();
    loadProducts();
    setupEventListeners();
    
    // إعداد Telegram Web App
    tg.MainButton.setText('إتمام الطلب');
    tg.MainButton.hide();
});

// تحميل الفئات
async function loadCategories() {
    try {
        // إذا كان API متاحاً، استخدمه
        if (API_BASE_URL) {
            const response = await fetch(`${API_BASE_URL}/api/categories`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ type: 'get_categories' })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    categories = data.categories;
                    renderCategories();
                    return;
                }
            }
        }

        // استخدام البيانات المحلية
        categories = [
            { id: 1, name: 'الإلكترونيات', icon: '📱' },
            { id: 2, name: 'الملابس', icon: '👕' },
            { id: 3, name: 'المنزل والحديقة', icon: '🏠' },
            { id: 4, name: 'الكتب', icon: '📚' },
            { id: 5, name: 'الرياضة', icon: '⚽' }
        ];
        renderCategories();
    } catch (error) {
        console.error('خطأ في تحميل الفئات:', error);
        // استخدام بيانات افتراضية
        categories = [
            { id: 1, name: 'الإلكترونيات', icon: '📱' },
            { id: 2, name: 'الملابس', icon: '👕' },
            { id: 3, name: 'المنزل', icon: '🏠' },
            { id: 4, name: 'الكتب', icon: '📚' },
            { id: 5, name: 'الرياضة', icon: '⚽' }
        ];
        renderCategories();
    }
}

// تحميل المنتجات
async function loadProducts() {
    try {
        // إذا كان API متاحاً، استخدمه
        if (API_BASE_URL) {
            const response = await fetch(`${API_BASE_URL}/api/products`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ type: 'get_products' })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    products = data.products;
                    renderProducts();
                    return;
                }
            }
        }

        // استخدام البيانات المحلية
        products = [
            {
                id: 1,
                name: 'هاتف ذكي',
                description: 'هاتف ذكي بمواصفات عالية وكاميرا متطورة',
                price: 2500,
                category_id: 1,
                stock_quantity: 50,
                image: '📱'
            },
            {
                id: 2,
                name: 'لابتوب',
                description: 'لابتوب للألعاب والعمل بمعالج قوي',
                price: 15000,
                category_id: 1,
                stock_quantity: 20,
                image: '💻'
            },
            {
                id: 3,
                name: 'قميص قطني',
                description: 'قميص قطني مريح ومناسب لجميع المناسبات',
                price: 150,
                category_id: 2,
                stock_quantity: 100,
                image: '👕'
            },
            {
                id: 4,
                name: 'بنطلون جينز',
                description: 'بنطلون جينز عالي الجودة ومقاوم للتمزق',
                price: 300,
                category_id: 2,
                stock_quantity: 75,
                image: '👖'
            },
            {
                id: 5,
                name: 'مصباح LED',
                description: 'مصباح LED موفر للطاقة وإضاءة ممتازة',
                price: 80,
                category_id: 3,
                stock_quantity: 200,
                image: '💡'
            },
            {
                id: 6,
                name: 'كتاب البرمجة',
                description: 'كتاب تعلم البرمجة للمبتدئين خطوة بخطوة',
                price: 120,
                category_id: 4,
                stock_quantity: 30,
                image: '📖'
            },
            {
                id: 7,
                name: 'كرة قدم',
                description: 'كرة قدم احترافية مناسبة للملاعب العشبية',
                price: 200,
                category_id: 5,
                stock_quantity: 40,
                image: '⚽'
            }
        ];

        renderProducts();
    } catch (error) {
        console.error('خطأ في تحميل المنتجات:', error);
        // استخدام بيانات افتراضية
        products = [
            { id: 1, name: 'هاتف ذكي', price: 5000, category_id: 1, image: '📱', description: 'هاتف ذكي حديث' },
            { id: 2, name: 'قميص قطني', price: 200, category_id: 2, image: '👕', description: 'قميص قطني مريح' },
            { id: 3, name: 'كتاب تقني', price: 150, category_id: 4, image: '📚', description: 'كتاب في البرمجة' }
        ];
        renderProducts();
    }
}

// عرض الفئات
function renderCategories() {
    const categoriesContainer = document.getElementById('categories');
    
    categories.forEach(category => {
        const button = document.createElement('button');
        button.className = 'category-btn';
        button.setAttribute('data-category', category.id);
        button.innerHTML = `${category.icon} ${category.name}`;
        button.onclick = () => filterByCategory(category.id);
        categoriesContainer.appendChild(button);
    });
}

// عرض المنتجات
function renderProducts(filteredProducts = null) {
    const container = document.getElementById('products-container');
    const productsToShow = filteredProducts || products;
    
    if (productsToShow.length === 0) {
        container.innerHTML = '<div class="empty-cart">📦 لا توجد منتجات في هذه الفئة</div>';
        return;
    }
    
    const grid = document.createElement('div');
    grid.className = 'products-grid';
    
    productsToShow.forEach(product => {
        const productCard = createProductCard(product);
        grid.appendChild(productCard);
    });
    
    container.innerHTML = '';
    container.appendChild(grid);
}

// إنشاء بطاقة منتج
function createProductCard(product) {
    const card = document.createElement('div');
    card.className = 'product-card';
    
    const cartItem = cart.find(item => item.id === product.id);
    const quantity = cartItem ? cartItem.quantity : 0;
    
    card.innerHTML = `
        <div class="product-image">${product.image}</div>
        <div class="product-name">${product.name}</div>
        <div class="product-description">${product.description}</div>
        <div class="product-price">${product.price} جنيه</div>
        <div class="product-actions">
            ${quantity > 0 ? `
                <div class="quantity-controls">
                    <button class="quantity-btn" onclick="updateQuantity(${product.id}, ${quantity - 1})">-</button>
                    <span class="quantity-display">${quantity}</span>
                    <button class="quantity-btn" onclick="updateQuantity(${product.id}, ${quantity + 1})">+</button>
                </div>
            ` : `
                <button class="add-to-cart" onclick="addToCart(${product.id})">
                    <i class="fas fa-cart-plus"></i> إضافة للسلة
                </button>
            `}
        </div>
    `;
    
    return card;
}

// تصفية المنتجات حسب الفئة
function filterByCategory(categoryId) {
    // تحديث أزرار الفئات
    document.querySelectorAll('.category-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    if (categoryId === 'all') {
        document.querySelector('[data-category="all"]').classList.add('active');
        renderProducts();
    } else {
        document.querySelector(`[data-category="${categoryId}"]`).classList.add('active');
        const filtered = products.filter(product => product.category_id === categoryId);
        renderProducts(filtered);
    }
}

// إضافة منتج للسلة
function addToCart(productId) {
    const product = products.find(p => p.id === productId);
    if (!product) return;
    
    const existingItem = cart.find(item => item.id === productId);
    if (existingItem) {
        existingItem.quantity += 1;
    } else {
        cart.push({
            id: product.id,
            name: product.name,
            price: product.price,
            quantity: 1
        });
    }
    
    updateCartDisplay();
    renderProducts(); // إعادة عرض المنتجات لتحديث الأزرار
    
    // تأثير بصري
    tg.HapticFeedback.impactOccurred('light');
}

// تحديث كمية المنتج
function updateQuantity(productId, newQuantity) {
    if (newQuantity <= 0) {
        cart = cart.filter(item => item.id !== productId);
    } else {
        const item = cart.find(item => item.id === productId);
        if (item) {
            item.quantity = newQuantity;
        }
    }
    
    updateCartDisplay();
    renderProducts(); // إعادة عرض المنتجات لتحديث الأزرار
    
    tg.HapticFeedback.impactOccurred('light');
}

// تحديث عرض السلة
function updateCartDisplay() {
    const cartCount = cart.reduce((sum, item) => sum + item.quantity, 0);
    const cartTotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    
    document.getElementById('cart-count').textContent = cartCount;
    document.getElementById('cart-total').textContent = cartTotal;
    document.getElementById('checkout-btn').textContent = `إتمام الطلب - ${cartTotal} جنيه`;
    document.getElementById('final-total').textContent = cartTotal;
    
    // تفعيل/إلغاء تفعيل زر الطلب
    const checkoutBtn = document.getElementById('checkout-btn');
    if (cartCount > 0) {
        checkoutBtn.disabled = false;
        tg.MainButton.setText(`إتمام الطلب - ${cartTotal} جنيه`);
        tg.MainButton.show();
    } else {
        checkoutBtn.disabled = true;
        tg.MainButton.hide();
    }
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // زر إتمام الطلب
    document.getElementById('checkout-btn').addEventListener('click', openCheckoutModal);
    
    // زر Telegram الرئيسي
    tg.MainButton.onClick(openCheckoutModal);
    
    // نموذج إتمام الطلب
    document.getElementById('checkout-form').addEventListener('submit', submitOrder);
    
    // طرق الدفع
    document.querySelectorAll('.payment-method').forEach(method => {
        method.addEventListener('click', function() {
            document.querySelectorAll('.payment-method').forEach(m => m.classList.remove('selected'));
            this.classList.add('selected');
            selectedPaymentMethod = this.getAttribute('data-method');
        });
    });
    
    // تحديد طريقة الدفع الافتراضية
    document.querySelector('[data-method="cash"]').classList.add('selected');
}

// فتح نموذج إتمام الطلب
function openCheckoutModal() {
    if (cart.length === 0) {
        tg.showAlert('السلة فارغة! أضف منتجات أولاً');
        return;
    }
    
    document.getElementById('checkout-modal').style.display = 'block';
    tg.MainButton.hide();
}

// إغلاق نموذج إتمام الطلب
function closeCheckoutModal() {
    document.getElementById('checkout-modal').style.display = 'none';
    if (cart.length > 0) {
        tg.MainButton.show();
    }
}

// إرسال الطلب
async function submitOrder(event) {
    event.preventDefault();
    
    const name = document.getElementById('customer-name').value.trim();
    const phone = document.getElementById('customer-phone').value.trim();
    const address = document.getElementById('customer-address').value.trim();
    const notes = document.getElementById('order-notes').value.trim();
    
    if (!name || !phone || !address) {
        tg.showAlert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    if (!selectedPaymentMethod) {
        tg.showAlert('يرجى اختيار طريقة الدفع');
        return;
    }
    
    const orderData = {
        type: 'new_order',
        items: cart,
        total: cart.reduce((sum, item) => sum + (item.price * item.quantity), 0),
        delivery_info: {
            name: name,
            phone: phone,
            address: address,
            notes: notes
        },
        payment_method: selectedPaymentMethod,
        date: new Date().toLocaleString('ar-EG')
    };
    
    try {
        // إرسال البيانات إلى Telegram
        tg.sendData(JSON.stringify(orderData));
        tg.close();
    } catch (error) {
        console.error('خطأ في إرسال الطلب:', error);
        tg.showAlert('حدث خطأ في إرسال الطلب. يرجى المحاولة مرة أخرى');
    }
}

// إغلاق النموذج عند النقر خارجه
window.onclick = function(event) {
    const modal = document.getElementById('checkout-modal');
    if (event.target === modal) {
        closeCheckoutModal();
    }
}
