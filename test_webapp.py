#!/usr/bin/env python3
"""
اختبار إرسال بيانات Web App للبوت
"""

import asyncio
import json
import aiomysql

# إعدادات قاعدة البيانات
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '',
    'db': 'telegram_store',
    'charset': 'utf8mb4'
}

async def check_database_connection():
    """التحقق من الاتصال بقاعدة البيانات"""
    try:
        print("🔄 اختبار الاتصال بقاعدة البيانات...")
        conn = await aiomysql.connect(**DB_CONFIG)
        cur = await conn.cursor(aiomysql.DictCursor)

        # عرض جميع المستخدمين
        await cur.execute("SELECT id, name FROM users ORDER BY id")
        users = await cur.fetchall()

        print(f"👥 المستخدمون في قاعدة البيانات ({len(users)}):")
        for user in users:
            print(f"   - ID: {user['id']}, الاسم: {user['name']}")

        conn.close()
        return users

    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        return []

async def simulate_web_app_order():
    """محاكاة إنشاء طلب مباشرة في قاعدة البيانات"""

    # أولاً، التحقق من المستخدمين المتاحين
    users = await check_database_connection()
    if not users:
        return

    # اختيار أول مستخدم غير المدير
    test_user = None
    for user in users:
        if user['id'] != 1856344728:  # تجنب المدير Ahmed
            test_user = user
            break

    if not test_user:
        print("❌ لا يوجد مستخدمون غير مديرين")
        return

    test_user_id = test_user['id']
    test_order = {
        "items": [
            {
                "id": 1,
                "name": "هاتف ذكي",
                "price": 2500,
                "quantity": 1
            }
        ],
        "total": 2500,
        "delivery_info": {
            "name": "مستخدم تجريبي",
            "phone": "01234567890",
            "address": "عنوان تجريبي",
            "notes": "ملاحظات تجريبية"
        },
        "payment_method": "cash"
    }

    print(f"\n🧪 محاكاة إنشاء طلب للمستخدم: {test_user['name']} (ID: {test_user_id})")

    try:
        # الاتصال بقاعدة البيانات
        conn = await aiomysql.connect(**DB_CONFIG)
        cur = await conn.cursor(aiomysql.DictCursor)

        # إنشاء الطلب
        await cur.execute("""
            INSERT INTO orders (user_id, total, status, delivery_info, payment_method, created_at)
            VALUES (%s, %s, %s, %s, %s, NOW())
        """, (
            test_user_id,
            test_order['total'],
            'pending',
            json.dumps(test_order['delivery_info'], ensure_ascii=False),
            test_order['payment_method']
        ))

        order_id = cur.lastrowid
        print(f"✅ تم إنشاء الطلب برقم: {order_id}")

        # إضافة عناصر الطلب
        for item in test_order['items']:
            await cur.execute("""
                INSERT INTO order_items (order_id, product_id, quantity, price)
                VALUES (%s, %s, %s, %s)
            """, (order_id, item['id'], item['quantity'], item['price']))

            print(f"✅ تم إضافة المنتج: {item['name']}")

        await conn.commit()
        print("🎉 تم إنشاء الطلب بنجاح!")

        # التحقق من الطلب
        await cur.execute("""
            SELECT o.*, u.name as user_name
            FROM orders o
            JOIN users u ON o.user_id = u.id
            WHERE o.id = %s
        """, (order_id,))

        created_order = await cur.fetchone()
        print(f"📋 تفاصيل الطلب المُنشأ:")
        print(f"   - رقم الطلب: {created_order['id']}")
        print(f"   - اسم العميل: {created_order['user_name']}")
        print(f"   - المجموع: {created_order['total']} جنيه")
        print(f"   - الحالة: {created_order['status']}")

        conn.close()

    except Exception as e:
        print(f"❌ خطأ في المحاكاة: {e}")

if __name__ == "__main__":
    asyncio.run(simulate_web_app_order())
