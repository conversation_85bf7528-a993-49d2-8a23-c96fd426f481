<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>متجر تليجرام</title>
    <script src="https://telegram.org/js/telegram-web-app.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 10px;
            color: #333;
        }

        .container {
            max-width: 400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .header {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            padding: 20px;
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(255,255,255,0.1) 10px,
                rgba(255,255,255,0.1) 20px
            );
            animation: slide 20s linear infinite;
        }

        @keyframes slide {
            0% { transform: translateX(-50px) translateY(-50px); }
            100% { transform: translateX(50px) translateY(50px); }
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
            position: relative;
            z-index: 1;
        }

        .header p {
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .user-info {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            padding: 15px 20px;
            color: white;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: bold;
        }

        .user-details h3 {
            margin-bottom: 5px;
        }

        .user-role {
            background: rgba(255,255,255,0.2);
            padding: 3px 10px;
            border-radius: 15px;
            font-size: 12px;
        }

        .tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 2px solid #e9ecef;
        }

        .tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #ddd6fe, #c7d2fe);
            border: none;
            font-weight: bold;
        }

        .tab.active {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
            transform: translateY(-2px);
        }

        .tab:hover:not(.active) {
            background: linear-gradient(135deg, #c7d2fe, #a5b4fc);
        }

        .content {
            padding: 20px;
            min-height: 400px;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .product-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .product-image {
            height: 120px;
            background: linear-gradient(135deg, #fa709a, #fee140);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 30px;
            color: white;
        }

        .product-info {
            padding: 15px;
        }

        .product-name {
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .product-price {
            color: #e74c3c;
            font-weight: bold;
            font-size: 16px;
        }

        .add-product-btn {
            background: linear-gradient(135deg, #00b894, #00a085);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 15px;
            width: 100%;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .add-product-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,184,148,0.3);
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }

        .form-group input, .form-group textarea, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus, .form-group textarea:focus, .form-group select:focus {
            outline: none;
            border-color: #8b5cf6;
        }

        .orders-list {
            space-y: 15px;
        }

        .order-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 15px;
            border-left: 5px solid #3498db;
        }

        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .order-id {
            font-weight: bold;
            color: #2980b9;
        }

        .order-status {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-completed {
            background: #d1edff;
            color: #0c5460;
        }

        .cart-item {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .cart-total {
            background: linear-gradient(135deg, #fd79a8, #e84393);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-top: 20px;
        }

        .checkout-btn {
            background: linear-gradient(135deg, #00b894, #00a085);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin-top: 15px;
            width: 100%;
            transition: all 0.3s ease;
        }

        .checkout-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,184,148,0.3);
        }

        .floating-cart {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: linear-gradient(135deg, #fd79a8, #e84393);
            color: white;
            border: none;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            font-size: 20px;
            cursor: pointer;
            box-shadow: 0 10px 25px rgba(253,121,168,0.3);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .floating-cart:hover {
            transform: scale(1.1);
        }

        .cart-count {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .hidden {
            display: none;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #8b5cf6;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛍️ متجر تليجرام</h1>
            <p>متجرك الإلكتروني المتكامل</p>
        </div>

        <div class="user-info">
            <div class="user-avatar" id="userAvatar">👤</div>
            <div class="user-details">
                <h3 id="userName">مرحباً بك</h3>
                <span class="user-role" id="userRole">عميل</span>
            </div>
        </div>

        <div class="tabs">
            <button class="tab active" data-tab="products">المنتجات</button>
            <button class="tab" data-tab="cart">السلة</button>
            <button class="tab" data-tab="orders">الطلبات</button>
            <button class="tab" data-tab="admin" id="adminTab" style="display: none;">الإدارة</button>
        </div>

        <div class="content">
            <!-- Products Tab -->
            <div id="products" class="tab-content">
                <div class="products-grid" id="productsGrid">
                    <!-- Products will be loaded here -->
                </div>
            </div>

            <!-- Cart Tab -->
            <div id="cart" class="tab-content hidden">
                <div id="cartItems"></div>
                <div class="cart-total" id="cartTotal" style="display: none;">
                    <h3>الإجمالي: <span id="totalAmount">0</span> ج.م</h3>
                    <button class="checkout-btn" onclick="checkout()">إتمام الطلب</button>
                </div>
            </div>

            <!-- Orders Tab -->
            <div id="orders" class="tab-content hidden">
                <div class="orders-list" id="ordersList">
                    <!-- Orders will be loaded here -->
                </div>
            </div>

            <!-- Admin Tab -->
            <div id="admin" class="tab-content hidden">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="totalProducts">0</div>
                        <div class="stat-label">إجمالي المنتجات</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="totalOrders">0</div>
                        <div class="stat-label">إجمالي الطلبات</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="totalRevenue">0</div>
                        <div class="stat-label">إجمالي الإيرادات</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="totalUsers">0</div>
                        <div class="stat-label">إجمالي المستخدمين</div>
                    </div>
                </div>

                <button class="add-product-btn" onclick="showAddProductForm()">إضافة منتج جديد</button>
                
                <div id="addProductForm" class="hidden">
                    <div class="form-group">
                        <label>اسم المنتج:</label>
                        <input type="text" id="productName" placeholder="أدخل اسم المنتج">
                    </div>
                    <div class="form-group">
                        <label>السعر:</label>
                        <input type="number" id="productPrice" placeholder="السعر بالجنيه المصري">
                    </div>
                    <div class="form-group">
                        <label>الوصف:</label>
                        <textarea id="productDescription" placeholder="وصف المنتج"></textarea>
                    </div>
                    <div class="form-group">
                        <label>التصنيف:</label>
                        <select id="productCategory">
                            <option value="electronics">إلكترونيات</option>
                            <option value="clothing">ملابس</option>
                            <option value="books">كتب</option>
                            <option value="home">منزل</option>
                            <option value="sports">رياضة</option>
                        </select>
                    </div>
                    <button class="add-product-btn" onclick="addProduct()">حفظ المنتج</button>
                </div>
            </div>
        </div>
    </div>

    <div class="floating-cart" onclick="showCart()" id="floatingCart">
        🛒
        <span class="cart-count" id="cartCount">0</span>
    </div>

    <script>
        // Initialize Telegram Web App
        let tg = window.Telegram.WebApp;
        let user = tg.initDataUnsafe?.user;

        // App State
        let currentUser = {
            id: user?.id || 12345,
            name: user?.first_name || 'مستخدم تجريبي',
            username: user?.username || 'testuser',
            role: 'customer' // or 'admin'
        };

        let products = [
            {id: 1, name: 'لابتوب Dell', price: 15000, category: 'electronics', emoji: '💻'},
            {id: 2, name: 'قميص قطني', price: 250, category: 'clothing', emoji: '👕'},
            {id: 3, name: 'كتاب البرمجة', price: 180, category: 'books', emoji: '📚'},
            {id: 4, name: 'ساعة ذكية', price: 800, category: 'electronics', emoji: '⌚'},
            {id: 5, name: 'حقيبة يد', price: 450, category: 'clothing', emoji: '👜'},
            {id: 6, name: 'سماعات لاسلكية', price: 320, category: 'electronics', emoji: '🎧'}
        ];

        let cart = [];
        let orders = [];
        let users = [currentUser];

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            initApp();
            loadProducts();
            updateCartUI();
            updateStats();
            
            // Set Telegram theme
            if (tg.colorScheme === 'dark') {
                document.body.style.background = 'linear-gradient(135deg, #2c3e50 0%, #3498db 100%)';
            }
        });

        function initApp() {
            // Set user info
            document.getElementById('userName').textContent = currentUser.name;
            document.getElementById('userAvatar').textContent = currentUser.name.charAt(0);
            
            // Check if user is admin
            if (currentUser.role === 'admin' || currentUser.id === 12345) {
                currentUser.role = 'admin';
                document.getElementById('userRole').textContent = 'مدير';
                document.getElementById('adminTab').style.display = 'block';
            }

            // Setup tab navigation
            setupTabs();
            
            // Configure Telegram WebApp
            tg.expand();
            tg.ready();
        }

        function setupTabs() {
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    const tabName = tab.dataset.tab;
                    switchTab(tabName);
                });
            });
        }

        function switchTab(tabName) {
            // Update active tab
            document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
            
            // Show/hide content
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });
            document.getElementById(tabName).classList.remove('hidden');
            
            // Load specific content
            if (tabName === 'orders') loadOrders();
            if (tabName === 'admin') updateStats();
        }

        function loadProducts() {
            const grid = document.getElementById('productsGrid');
            grid.innerHTML = products.map(product => `
                <div class="product-card" onclick="addToCart(${product.id})">
                    <div class="product-image">${product.emoji}</div>
                    <div class="product-info">
                        <div class="product-name">${product.name}</div>
                        <div class="product-price">${product.price} ج.م</div>
                    </div>
                </div>
            `).join('');
        }

        function addToCart(productId) {
            const product = products.find(p => p.id === productId);
            const existingItem = cart.find(item => item.id === productId);
            
            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cart.push({...product, quantity: 1});
            }
            
            updateCartUI();
            
            // Haptic feedback
            tg.HapticFeedback.impactOccurred('light');
            
            // Show notification
            tg.showAlert(`تم إضافة ${product.name} إلى السلة`);
        }

        function updateCartUI() {
            const cartCount = cart.reduce((sum, item) => sum + item.quantity, 0);
            document.getElementById('cartCount').textContent = cartCount;
            
            const cartItems = document.getElementById('cartItems');
            const cartTotal = document.getElementById('cartTotal');
            
            if (cart.length === 0) {
                cartItems.innerHTML = '<p style="text-align: center; color: #666; padding: 50px;">السلة فارغة</p>';
                cartTotal.style.display = 'none';
                return;
            }
            
            cartItems.innerHTML = cart.map(item => `
                <div class="cart-item">
                    <div>
                        <strong>${item.name}</strong><br>
                        <small>${item.price} ج.م × ${item.quantity}</small>
                    </div>
                    <div>
                        <button onclick="changeQuantity(${item.id}, -1)" style="background: #e74c3c; color: white; border: none; padding: 5px 10px; border-radius: 5px; margin-left: 5px;">-</button>
                        <span>${item.quantity}</span>
                        <button onclick="changeQuantity(${item.id}, 1)" style="background: #27ae60; color: white; border: none; padding: 5px 10px; border-radius: 5px; margin-right: 5px;">+</button>
                    </div>
                </div>
            `).join('');
            
            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            document.getElementById('totalAmount').textContent = total;
            cartTotal.style.display = 'block';
        }

        function changeQuantity(productId, change) {
            const item = cart.find(item => item.id === productId);
            if (item) {
                item.quantity += change;
                if (item.quantity <= 0) {
                    cart = cart.filter(item => item.id !== productId);
                }
                updateCartUI();
            }
        }

        function showCart() {
            switchTab('cart');
        }

        function checkout() {
            if (cart.length === 0) return;
            
            const order = {
                id: Date.now(),
                userId: currentUser.id,
                items: [...cart],
                total: cart.reduce((sum, item) => sum + (item.price * item.quantity), 0),
                status: 'pending',
                date: new Date().toLocaleDateString('ar-EG')
            };
            
            orders.push(order);
            
            // إرسال الطلب للبوت أولاً
            sendOrderToBot(order);
            
            // مسح السلة وتحديث الواجهة
            cart = [];
            updateCartUI();
            
            // إغلاق التطبيق والعودة للشات
            tg.close();
        }

        function sendOrderToBot(order) {
            // إرسال الطلب عبر Telegram WebApp
            const orderData = {
                type: 'new_order',
                order: {
                    id: order.id,
                    customer: {
                        id: currentUser.id,
                        name: currentUser.name,
                        username: currentUser.username
                    },
                    items: order.items,
                    total: order.total,
                    date: order.date,
                    status: order.status
                }
            };
            
            // إرسال البيانات إلى البوت
            tg.sendData(JSON.stringify(orderData));
            
            // إرسال إشعار للمدير أيضاً
            if (typeof window.sendNotificationToAdmin === 'function') {
                window.sendNotificationToAdmin(orderData);
            }
        }

        function loadOrders() {
            const ordersList = document.getElementById('ordersList');
            const userOrders = orders.filter(order => order.userId === currentUser.id || currentUser.role === 'admin');
            
            if (userOrders.length === 0) {
                ordersList.innerHTML = '<p style="text-align: center; color: #666; padding: 50px;">لا توجد طلبات</p>';
                return;
            }
            
            ordersList.innerHTML = userOrders.map(order => `
                <div class="order-card">
                    <div class="order-header">
                        <span class="order-id">طلب #${order.id}</span>
                        <span class="order-status ${order.status === 'completed' ? 'status-completed' : 'status-pending'}">
                            ${order.status === 'completed' ? 'مكتمل' : 'قيد المعالجة'}
                        </span>
                    </div>
                    <p><strong>التاريخ:</strong> ${order.date}</p>
                    <p><strong>المنتجات:</strong> ${order.items.length} منتج</p>
                    <p><strong>الإجمالي:</strong> ${order.total} ج.م</p>
                    ${currentUser.role === 'admin' ? `
                        <button onclick="toggleOrderStatus(${order.id})" 
                                style="background: #3498db; color: white; border: none; padding: 8px 15px; border-radius: 5px; margin-top: 10px;">
                            ${order.status === 'completed' ? 'تغيير إلى قيد المعالجة' : 'تغيير إلى مكتمل'}
                        </button>
                    ` : ''}
                </div>
            `).join('');
        }

        function toggleOrderStatus(orderId) {
            const order = orders.find(o => o.id === orderId);
            if (order) {
                order.status = order.status === 'completed' ? 'pending' : 'completed';
                loadOrders();
                updateStats();
            }
        }

        function showAddProductForm() {
            const form = document.getElementById('addProductForm');
            form.classList.toggle('hidden');
        }

        function addProduct() {
            const name = document.getElementById('productName').value;
            const price = parseFloat(document.getElementById('productPrice').value);
            const description = document.getElementById('productDescription').value;
            const category = document.getElementById('productCategory').value;
            
            if (!name || !price) {
                tg.showAlert('يرجى ملء جميع الحقول المطلوبة');
                return;
            }
            
            const categoryEmojis = {
                electronics: '📱',
                clothing: '👕',
                books: '📚',
                home: '🏠',
                sports: '⚽'
            };
            
            const newProduct = {
                id: Date.now(),
                name,
                price,
                description,
                category,
                emoji: categoryEmojis[category]
            };
            
            products.push(newProduct);
            loadProducts();
            updateStats();
            
            // Clear form
            document.getElementById('productName').value = '';
            document.getElementById('productPrice').value = '';
            document.getElementById('productDescription').value = '';
            document.getElementById('addProductForm').classList.add('hidden');
            
            tg.HapticFeedback.notificationOccurred('success');
            tg.showAlert('تم إضافة المنتج بنجاح!');
        }

        function updateStats() {
            if (currentUser.role !== 'admin') return;
            
            document.getElementById('totalProducts').textContent = products.length;
            document.getElementById('totalOrders').textContent = orders.length;
            document.getElementById('totalRevenue').textContent = orders.reduce((sum, order) => sum + order.total, 0);
            document.getElementById('totalUsers').textContent = users.length;
        }

        // Handle Telegram WebApp events
        tg.onEvent('mainButtonClicked', function() {
            checkout();
        });

        tg.onEvent('backButtonClicked', function() {
            // Handle back button
        });

        // Set main button
        function updateMainButton() {
            if (cart.length > 0) {
                tg.MainButton.setText(`إتمام الطلب (${cart.reduce((sum, item) => sum + (item.price * item.quantity), 0)} ج.م)`);
                tg.MainButton.show();
            } else {
                tg.MainButton.hide();
            }
        }

        // Update main button when cart changes
        const originalUpdateCartUI = updateCartUI;
        updateCartUI = function() {
            originalUpdateCartUI();
            updateMainButton();
        };
    </script>
</body>
</html>