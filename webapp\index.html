<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>متجر تليجرام</title>
    <script src="https://telegram.org/js/telegram-web-app.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--tg-theme-bg-color, #ffffff);
            color: var(--tg-theme-text-color, #000000);
            padding: 8px;
            direction: rtl;
            min-height: 100vh;
            font-size: 14px;
        }

        /* تحسينات للهواتف */
        @media (max-width: 480px) {
            body {
                padding: 5px;
                font-size: 13px;
            }
        }

        .header {
            background: var(--tg-theme-secondary-bg-color, #f8f9fa);
            padding: 15px;
            border-radius: 12px;
            margin-bottom: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            color: var(--tg-theme-text-color, #000);
            margin-bottom: 5px;
        }

        .cart-summary {
            background: var(--tg-theme-button-color, #007bff);
            color: var(--tg-theme-button-text-color, #ffffff);
            padding: 10px 15px;
            border-radius: 8px;
            margin: 10px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .categories {
            display: flex;
            overflow-x: auto;
            gap: 10px;
            margin-bottom: 20px;
            padding: 10px 0;
        }

        .category-btn {
            background: var(--tg-theme-secondary-bg-color, #f8f9fa);
            border: 2px solid var(--tg-theme-button-color, #007bff);
            color: var(--tg-theme-text-color, #000);
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            white-space: nowrap;
            transition: all 0.3s;
            min-width: 80px;
            text-align: center;
        }

        .category-btn.active {
            background: var(--tg-theme-button-color, #007bff);
            color: var(--tg-theme-button-text-color, #ffffff);
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 12px;
            margin-bottom: 100px;
        }

        /* تحسين للهواتف الصغيرة */
        @media (max-width: 480px) {
            .products-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }
        }

        .product-card {
            background: var(--tg-theme-secondary-bg-color, #f8f9fa);
            border-radius: 12px;
            padding: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s;
            min-height: 280px;
            display: flex;
            flex-direction: column;
        }

        @media (max-width: 480px) {
            .product-card {
                padding: 10px;
                min-height: 250px;
            }
        }

        .product-card:hover {
            transform: translateY(-2px);
        }

        .product-image {
            width: 100%;
            height: 200px;
            background: #e9ecef;
            border-radius: 8px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: #6c757d;
        }

        .product-name {
            font-weight: bold;
            margin-bottom: 5px;
            color: var(--tg-theme-text-color, #000);
        }

        .product-description {
            color: var(--tg-theme-hint-color, #6c757d);
            font-size: 14px;
            margin-bottom: 10px;
        }

        .product-price {
            font-size: 18px;
            font-weight: bold;
            color: var(--tg-theme-button-color, #007bff);
            margin-bottom: 10px;
        }

        .product-actions {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .quantity-btn {
            background: var(--tg-theme-button-color, #007bff);
            color: var(--tg-theme-button-text-color, #ffffff);
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .quantity-display {
            min-width: 30px;
            text-align: center;
            font-weight: bold;
        }

        .add-to-cart {
            background: var(--tg-theme-button-color, #007bff);
            color: var(--tg-theme-button-text-color, #ffffff);
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: opacity 0.3s;
        }

        .add-to-cart:hover {
            opacity: 0.8;
        }

        .checkout-section {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--tg-theme-bg-color, #ffffff);
            padding: 15px;
            border-top: 1px solid var(--tg-theme-secondary-bg-color, #f8f9fa);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }

        .checkout-btn {
            width: 100%;
            background: var(--tg-theme-button-color, #007bff);
            color: var(--tg-theme-button-text-color, #ffffff);
            border: none;
            padding: 15px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s;
        }

        .checkout-btn:hover {
            opacity: 0.8;
        }

        .checkout-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: var(--tg-theme-hint-color, #6c757d);
        }

        .empty-cart {
            text-align: center;
            padding: 50px;
            color: var(--tg-theme-hint-color, #6c757d);
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            background: var(--tg-theme-bg-color, #ffffff);
            margin: 20px auto;
            padding: 20px;
            border-radius: 12px;
            max-width: 400px;
            max-height: 90vh;
            overflow-y: auto;
            width: 90%;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--tg-theme-secondary-bg-color, #f8f9fa);
            border-radius: 6px;
            background: var(--tg-theme-bg-color, #ffffff);
            color: var(--tg-theme-text-color, #000);
        }

        .payment-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }

        .payment-method {
            padding: 15px;
            border: 2px solid var(--tg-theme-secondary-bg-color, #f8f9fa);
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }

        .payment-method.selected {
            border-color: var(--tg-theme-button-color, #007bff);
            background: var(--tg-theme-button-color, #007bff);
            color: var(--tg-theme-button-text-color, #ffffff);
        }

        .close-modal {
            float: left;
            font-size: 24px;
            cursor: pointer;
            color: var(--tg-theme-hint-color, #6c757d);
        }

        @media (max-width: 768px) {
            .products-grid {
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            }

            .categories {
                justify-content: center;
            }

            .modal-content {
                margin: 10px;
                max-width: calc(100vw - 20px);
                padding: 15px;
            }

            .form-group input,
            .form-group textarea,
            .form-group select {
                font-size: 16px; /* منع التكبير التلقائي في iOS */
            }

            .payment-methods {
                flex-direction: column;
                gap: 8px;
            }

            .payment-method {
                width: 100%;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛍️ متجر تليجرام</h1>
        <div class="cart-summary">
            <span>🛒 السلة: <span id="cart-count">0</span> منتج</span>
            <span>💰 المجموع: <span id="cart-total">0</span> جنيه</span>
        </div>
    </div>

    <div class="categories" id="categories">
        <button class="category-btn active" data-category="all">الكل</button>
    </div>

    <div id="products-container">
        <div class="loading">
            <i class="fas fa-spinner fa-spin"></i>
            <p>جاري تحميل المنتجات...</p>
        </div>
    </div>

    <div class="checkout-section">
        <button class="checkout-btn" id="checkout-btn" disabled>
            إتمام الطلب - 0 جنيه
        </button>
    </div>

    <!-- نموذج إتمام الطلب -->
    <div class="modal" id="checkout-modal">
        <div class="modal-content">
            <span class="close-modal" onclick="closeCheckoutModal()">&times;</span>
            <h2>إتمام الطلب</h2>
            
            <form id="checkout-form">
                <div class="form-group">
                    <label>الاسم الكامل *</label>
                    <input type="text" id="customer-name" required>
                </div>
                
                <div class="form-group">
                    <label>رقم الهاتف *</label>
                    <input type="tel" id="customer-phone" required>
                </div>
                
                <div class="form-group">
                    <label>العنوان *</label>
                    <textarea id="customer-address" rows="3" required></textarea>
                </div>
                
                <div class="form-group">
                    <label>ملاحظات إضافية</label>
                    <textarea id="order-notes" rows="2"></textarea>
                </div>
                
                <div class="form-group">
                    <label>طريقة الدفع *</label>
                    <div class="payment-methods">
                        <div class="payment-method" data-method="cash">
                            <i class="fas fa-money-bill"></i>
                            <p>نقداً عند الاستلام</p>
                        </div>
                        <div class="payment-method" data-method="card">
                            <i class="fas fa-credit-card"></i>
                            <p>بطاقة ائتمان</p>
                        </div>
                        <div class="payment-method" data-method="transfer">
                            <i class="fas fa-university"></i>
                            <p>تحويل بنكي</p>
                        </div>
                    </div>
                </div>
                
                <button type="submit" class="checkout-btn">
                    تأكيد الطلب - <span id="final-total">0</span> جنيه
                </button>
            </form>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
