<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>متجر تليجرام</title>
    <script src="https://telegram.org/js/telegram-web-app.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--tg-theme-bg-color, #ffffff);
            color: var(--tg-theme-text-color, #000000);
            padding: 8px;
            direction: rtl;
            min-height: 100vh;
            font-size: 14px;
            -webkit-text-size-adjust: 100%;
        }
        
        /* تحسينات للهواتف */
        @media (max-width: 480px) {
            body {
                padding: 5px;
                font-size: 13px;
            }
        }

        .header {
            background: var(--tg-theme-secondary-bg-color, #f8f9fa);
            padding: 12px;
            border-radius: 12px;
            margin-bottom: 15px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            color: var(--tg-theme-text-color, #000);
            margin-bottom: 8px;
            font-size: 1.5em;
        }

        .cart-summary {
            background: var(--tg-theme-button-color, #007bff);
            color: var(--tg-theme-button-text-color, #ffffff);
            padding: 8px 12px;
            border-radius: 8px;
            margin: 8px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9em;
        }

        .categories {
            display: flex;
            gap: 8px;
            margin-bottom: 15px;
            overflow-x: auto;
            padding: 5px 0;
            -webkit-overflow-scrolling: touch;
        }

        .category-btn {
            background: var(--tg-theme-secondary-bg-color, #f8f9fa);
            color: var(--tg-theme-text-color, #000);
            border: none;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
            white-space: nowrap;
            font-size: 0.85em;
            min-width: 80px;
        }

        .category-btn.active {
            background: var(--tg-theme-button-color, #007bff);
            color: var(--tg-theme-button-text-color, #ffffff);
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
            gap: 10px;
            margin-bottom: 80px;
        }
        
        /* تحسين للهواتف الصغيرة */
        @media (max-width: 480px) {
            .products-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 8px;
            }
        }

        .product-card {
            background: var(--tg-theme-secondary-bg-color, #f8f9fa);
            border-radius: 12px;
            padding: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            position: relative;
        }
        
        @media (max-width: 480px) {
            .product-card {
                padding: 8px;
                min-height: 180px;
            }
        }

        .product-card:active {
            transform: scale(0.98);
        }

        .product-image {
            font-size: 2.5em;
            text-align: center;
            margin-bottom: 8px;
        }

        .product-name {
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 0.9em;
            line-height: 1.2;
        }

        .product-price {
            color: var(--tg-theme-button-color, #007bff);
            font-weight: bold;
            margin-bottom: 8px;
            font-size: 0.95em;
        }

        .product-description {
            color: var(--tg-theme-hint-color, #6c757d);
            font-size: 0.8em;
            margin-bottom: 10px;
            flex-grow: 1;
            line-height: 1.3;
        }

        .add-to-cart-btn {
            background: var(--tg-theme-button-color, #007bff);
            color: var(--tg-theme-button-text-color, #ffffff);
            border: none;
            padding: 8px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.8em;
            transition: all 0.3s;
            margin-top: auto;
        }

        .add-to-cart-btn:active {
            background: var(--tg-theme-button-color, #0056b3);
            transform: scale(0.95);
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: var(--tg-theme-hint-color, #6c757d);
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: var(--tg-theme-bg-color, #ffffff);
            margin: 10px;
            padding: 20px;
            border-radius: 12px;
            max-width: 400px;
            max-height: 90vh;
            overflow-y: auto;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: calc(100% - 20px);
        }

        .close {
            color: var(--tg-theme-hint-color, #aaa);
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            color: var(--tg-theme-text-color, #000);
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            font-size: 0.9em;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--tg-theme-hint-color, #ddd);
            border-radius: 8px;
            font-size: 16px; /* منع التكبير التلقائي في iOS */
            background: var(--tg-theme-bg-color, #ffffff);
            color: var(--tg-theme-text-color, #000000);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .payment-methods {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .payment-method {
            flex: 1;
            min-width: 120px;
            padding: 10px;
            border: 2px solid var(--tg-theme-hint-color, #ddd);
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 0.85em;
        }

        .payment-method.selected {
            border-color: var(--tg-theme-button-color, #007bff);
            background: var(--tg-theme-button-color, #007bff);
            color: var(--tg-theme-button-text-color, #ffffff);
        }

        .submit-btn {
            width: 100%;
            background: var(--tg-theme-button-color, #007bff);
            color: var(--tg-theme-button-text-color, #ffffff);
            border: none;
            padding: 12px;
            border-radius: 8px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s;
            margin-top: 10px;
        }

        .submit-btn:active {
            background: var(--tg-theme-button-color, #0056b3);
            transform: scale(0.98);
        }

        /* تحسينات إضافية للهواتف */
        @media (max-width: 768px) {
            .categories {
                justify-content: flex-start;
            }
            
            .modal-content {
                margin: 10px;
                max-width: calc(100vw - 20px);
                padding: 15px;
            }
            
            .payment-methods {
                flex-direction: column;
                gap: 8px;
            }
            
            .payment-method {
                width: 100%;
                text-align: center;
            }
        }

        /* تحسينات للأجهزة اللوحية */
        @media (min-width: 481px) and (max-width: 768px) {
            .products-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (min-width: 769px) {
            .products-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛍️ متجر تليجرام</h1>
        <div class="cart-summary">
            <span>🛒 السلة: <span id="cart-count">0</span> منتج</span>
            <span>💰 المجموع: <span id="cart-total">0</span> جنيه</span>
        </div>
    </div>

    <div class="categories" id="categories">
        <button class="category-btn active" data-category="all">الكل</button>
    </div>

    <div id="products-container">
        <div class="loading">جاري تحميل المنتجات...</div>
    </div>

    <!-- Modal إتمام الطلب -->
    <div id="checkout-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>إتمام الطلب</h2>
            <hr style="margin: 15px 0;">
            
            <form id="checkout-form">
                <div class="form-group">
                    <label for="customer-name">الاسم الكامل *</label>
                    <input type="text" id="customer-name" required>
                </div>
                
                <div class="form-group">
                    <label for="customer-phone">رقم الهاتف *</label>
                    <input type="tel" id="customer-phone" required>
                </div>
                
                <div class="form-group">
                    <label for="customer-address">العنوان *</label>
                    <textarea id="customer-address" required></textarea>
                </div>
                
                <div class="form-group">
                    <label for="customer-notes">ملاحظات إضافية</label>
                    <textarea id="customer-notes"></textarea>
                </div>
                
                <div class="form-group">
                    <label>طريقة الدفع *</label>
                    <div class="payment-methods">
                        <div class="payment-method selected" data-method="cash">
                            💰 نقداً عند الاستلام
                        </div>
                        <div class="payment-method" data-method="card">
                            💳 بطاقة ائتمان
                        </div>
                        <div class="payment-method" data-method="transfer">
                            🏦 تحويل بنكي
                        </div>
                        <div class="payment-method" data-method="wallet">
                            📱 محفظة إلكترونية
                        </div>
                    </div>
                </div>
                
                <button type="submit" class="submit-btn">
                    تأكيد الطلب - <span id="final-total">0</span> جنيه
                </button>
            </form>
        </div>
    </div>

    <script>
        // إعداد Telegram Web App
        let tg = window.Telegram.WebApp;
        tg.expand();

        // متغيرات عامة
        let products = [];
        let categories = [];
        let cart = [];
        let selectedPaymentMethod = 'cash';

        // البيانات المحلية للمنتجات
        const localProducts = [
            { id: 1, name: 'هاتف ذكي Samsung', price: 8500, category_id: 1, image: '📱', description: 'هاتف ذكي بمواصفات عالية وكاميرا متطورة' },
            { id: 2, name: 'لابتوب Dell', price: 15000, category_id: 1, image: '💻', description: 'لابتوب للألعاب والعمل بمعالج قوي' },
            { id: 3, name: 'قميص قطني', price: 250, category_id: 2, image: '👕', description: 'قميص قطني مريح ومناسب للصيف' },
            { id: 4, name: 'فستان أنيق', price: 450, category_id: 2, image: '👗', description: 'فستان أنيق للمناسبات الخاصة' },
            { id: 5, name: 'كتاب البرمجة', price: 180, category_id: 4, image: '📚', description: 'كتاب شامل لتعلم البرمجة من الصفر' },
            { id: 6, name: 'رواية مشوقة', price: 120, category_id: 4, image: '📖', description: 'رواية مشوقة من أفضل الكتاب' },
            { id: 7, name: 'كرة قدم', price: 300, category_id: 5, image: '⚽', description: 'كرة قدم احترافية للتدريب واللعب' },
            { id: 8, name: 'دراجة هوائية', price: 2500, category_id: 5, image: '🚴', description: 'دراجة هوائية للرياضة والتنقل' }
        ];

        const localCategories = [
            { id: 1, name: 'الإلكترونيات', icon: '📱' },
            { id: 2, name: 'الملابس', icon: '👕' },
            { id: 3, name: 'المنزل', icon: '🏠' },
            { id: 4, name: 'الكتب', icon: '📚' },
            { id: 5, name: 'الرياضة', icon: '⚽' }
        ];

        // تحميل البيانات عند بدء التطبيق
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
            setupEventListeners();
            
            // إعداد Telegram Web App
            tg.MainButton.setText('إتمام الطلب');
            tg.MainButton.hide();
        });

        function loadData() {
            categories = localCategories;
            products = localProducts;
            renderCategories();
            renderProducts();
        }

        // عرض الفئات
        function renderCategories() {
            const container = document.getElementById('categories');
            const allBtn = container.querySelector('[data-category="all"]');

            categories.forEach(category => {
                const btn = document.createElement('button');
                btn.className = 'category-btn';
                btn.setAttribute('data-category', category.id);
                btn.innerHTML = `${category.icon} ${category.name}`;
                btn.addEventListener('click', () => filterProducts(category.id));
                container.appendChild(btn);
            });
        }

        // عرض المنتجات
        function renderProducts(filteredProducts = null) {
            const container = document.getElementById('products-container');
            const productsToShow = filteredProducts || products;

            if (productsToShow.length === 0) {
                container.innerHTML = '<div class="loading">لا توجد منتجات متاحة</div>';
                return;
            }

            const grid = document.createElement('div');
            grid.className = 'products-grid';

            productsToShow.forEach(product => {
                const card = document.createElement('div');
                card.className = 'product-card';
                card.innerHTML = `
                    <div class="product-image">${product.image}</div>
                    <div class="product-name">${product.name}</div>
                    <div class="product-price">${product.price} جنيه</div>
                    <div class="product-description">${product.description}</div>
                    <button class="add-to-cart-btn" onclick="addToCart(${product.id})">
                        إضافة للسلة
                    </button>
                `;
                grid.appendChild(card);
            });

            container.innerHTML = '';
            container.appendChild(grid);
        }

        // تصفية المنتجات
        function filterProducts(categoryId) {
            // تحديث أزرار الفئات
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            if (categoryId === 'all') {
                document.querySelector('[data-category="all"]').classList.add('active');
                renderProducts();
            } else {
                document.querySelector(`[data-category="${categoryId}"]`).classList.add('active');
                const filtered = products.filter(p => p.category_id == categoryId);
                renderProducts(filtered);
            }
        }

        // إضافة منتج للسلة
        function addToCart(productId) {
            const product = products.find(p => p.id === productId);
            if (!product) return;

            const existingItem = cart.find(item => item.id === productId);
            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cart.push({
                    id: product.id,
                    name: product.name,
                    price: product.price,
                    quantity: 1
                });
            }

            updateCartDisplay();

            // إظهار تأثير بصري
            const btn = event.target;
            btn.style.background = '#28a745';
            btn.textContent = 'تم الإضافة ✓';
            setTimeout(() => {
                btn.style.background = '';
                btn.textContent = 'إضافة للسلة';
            }, 1000);
        }

        // تحديث عرض السلة
        function updateCartDisplay() {
            const count = cart.reduce((sum, item) => sum + item.quantity, 0);
            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);

            document.getElementById('cart-count').textContent = count;
            document.getElementById('cart-total').textContent = total;
            document.getElementById('final-total').textContent = total;

            if (count > 0) {
                tg.MainButton.show();
            } else {
                tg.MainButton.hide();
            }
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // زر الفئة "الكل"
            document.querySelector('[data-category="all"]').addEventListener('click', () => filterProducts('all'));

            // زر Telegram الرئيسي
            tg.MainButton.onClick(openCheckoutModal);

            // نموذج إتمام الطلب
            document.getElementById('checkout-form').addEventListener('submit', submitOrder);

            // طرق الدفع
            document.querySelectorAll('.payment-method').forEach(method => {
                method.addEventListener('click', function() {
                    document.querySelectorAll('.payment-method').forEach(m => m.classList.remove('selected'));
                    this.classList.add('selected');
                    selectedPaymentMethod = this.getAttribute('data-method');
                });
            });

            // إغلاق النموذج
            document.querySelector('.close').addEventListener('click', closeCheckoutModal);

            // إغلاق النموذج عند النقر خارجه
            window.addEventListener('click', function(event) {
                const modal = document.getElementById('checkout-modal');
                if (event.target === modal) {
                    closeCheckoutModal();
                }
            });
        }

        // فتح نموذج إتمام الطلب
        function openCheckoutModal() {
            if (cart.length === 0) {
                tg.showAlert('السلة فارغة! أضف منتجات أولاً');
                return;
            }
            document.getElementById('checkout-modal').style.display = 'block';
        }

        // إغلاق نموذج إتمام الطلب
        function closeCheckoutModal() {
            document.getElementById('checkout-modal').style.display = 'none';
        }

        // إرسال الطلب
        async function submitOrder(event) {
            event.preventDefault();

            const name = document.getElementById('customer-name').value.trim();
            const phone = document.getElementById('customer-phone').value.trim();
            const address = document.getElementById('customer-address').value.trim();
            const notes = document.getElementById('customer-notes').value.trim();

            if (!name || !phone || !address) {
                tg.showAlert('يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            const orderData = {
                items: cart,
                total: cart.reduce((sum, item) => sum + (item.price * item.quantity), 0),
                customer: { name, phone, address, notes },
                payment_method: selectedPaymentMethod,
                timestamp: new Date().toISOString()
            };

            try {
                // إرسال البيانات إلى Telegram
                tg.sendData(JSON.stringify(orderData));
                tg.close();
            } catch (error) {
                console.error('خطأ في إرسال الطلب:', error);
                tg.showAlert('حدث خطأ في إرسال الطلب. يرجى المحاولة مرة أخرى');
            }
        }
    </script>
