#!/usr/bin/env python3
"""
API للحصول على البيانات من قاعدة البيانات
"""

import asyncio
import json
import aiomysql
from datetime import datetime

# إعدادات قاعدة البيانات (نفس إعدادات البوت)
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '',
    'db': 'matjer_bot',
    'autocommit': True
}

async def get_db_connection():
    """الاتصال بقاعدة البيانات"""
    return await aiomysql.connect(**DB_CONFIG)

async def get_categories():
    """جلب الفئات من قاعدة البيانات"""
    try:
        conn = await get_db_connection()
        async with conn.cursor(aiomysql.DictCursor) as cur:
            await cur.execute('SELECT * FROM categories WHERE is_active = TRUE ORDER BY id')
            categories = await cur.fetchall()
        conn.close()
        
        # تحويل البيانات للتنسيق المطلوب
        result = []
        for cat in categories:
            result.append({
                'id': cat['id'],
                'name': cat['name'],
                'description': cat['description'],
                'icon': get_category_icon(cat['name'])
            })
        
        return {'success': True, 'categories': result}
    except Exception as e:
        return {'success': False, 'error': str(e)}

async def get_products(category_id=None):
    """جلب المنتجات من قاعدة البيانات"""
    try:
        conn = await get_db_connection()
        async with conn.cursor(aiomysql.DictCursor) as cur:
            if category_id:
                await cur.execute('''
                    SELECT p.*, c.name as category_name 
                    FROM products p 
                    LEFT JOIN categories c ON p.category_id = c.id 
                    WHERE p.is_active = TRUE AND p.category_id = %s
                    ORDER BY p.id DESC
                ''', (category_id,))
            else:
                await cur.execute('''
                    SELECT p.*, c.name as category_name 
                    FROM products p 
                    LEFT JOIN categories c ON p.category_id = c.id 
                    WHERE p.is_active = TRUE
                    ORDER BY p.id DESC
                ''')
            products = await cur.fetchall()
        conn.close()
        
        # تحويل البيانات للتنسيق المطلوب
        result = []
        for prod in products:
            result.append({
                'id': prod['id'],
                'name': prod['name'],
                'description': prod['description'],
                'price': float(prod['price']),
                'category_id': prod['category_id'],
                'category_name': prod['category_name'],
                'stock_quantity': prod['stock_quantity'],
                'image': get_product_icon(prod['category_id']),
                'is_available': prod['stock_quantity'] > 0
            })
        
        return {'success': True, 'products': result}
    except Exception as e:
        return {'success': False, 'error': str(e)}

def get_category_icon(category_name):
    """إرجاع أيقونة الفئة"""
    icons = {
        'الإلكترونيات': '📱',
        'الملابس': '👕',
        'المنزل والحديقة': '🏠',
        'الكتب': '📚',
        'الرياضة': '⚽'
    }
    return icons.get(category_name, '📦')

def get_product_icon(category_id):
    """إرجاع أيقونة المنتج حسب الفئة"""
    icons = {
        1: '📱',  # الإلكترونيات
        2: '👕',  # الملابس
        3: '🏠',  # المنزل والحديقة
        4: '📚',  # الكتب
        5: '⚽'   # الرياضة
    }
    return icons.get(category_id, '📦')

async def save_order(order_data):
    """حفظ الطلب في قاعدة البيانات"""
    try:
        conn = await get_db_connection()
        async with conn.cursor() as cur:
            # حفظ الطلب
            await cur.execute('''
                INSERT INTO orders (user_id, items, total, status, payment_method, created_at, delivery_info, notes)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            ''', (
                order_data.get('user_id', 0),
                json.dumps(order_data.get('items', []), ensure_ascii=False),
                order_data.get('total', 0),
                'pending',
                order_data.get('payment_method', 'cash'),
                datetime.now(),
                json.dumps(order_data.get('delivery_info', {}), ensure_ascii=False),
                order_data.get('notes', '')
            ))
            
            order_id = cur.lastrowid
            
            # حفظ تفاصيل الطلب
            for item in order_data.get('items', []):
                await cur.execute('''
                    INSERT INTO order_items (order_id, product_id, quantity, price)
                    VALUES (%s, %s, %s, %s)
                ''', (order_id, item.get('id'), item.get('quantity'), item.get('price')))
        
        conn.close()
        return {'success': True, 'order_id': order_id}
    except Exception as e:
        return {'success': False, 'error': str(e)}

# دوال للاختبار
async def test_api():
    """اختبار API"""
    print("🧪 اختبار API...")
    
    # اختبار جلب الفئات
    print("\n📂 اختبار جلب الفئات:")
    categories = await get_categories()
    print(json.dumps(categories, ensure_ascii=False, indent=2))
    
    # اختبار جلب المنتجات
    print("\n📦 اختبار جلب المنتجات:")
    products = await get_products()
    print(json.dumps(products, ensure_ascii=False, indent=2))
    
    # اختبار جلب منتجات فئة معينة
    print("\n📱 اختبار جلب منتجات الإلكترونيات:")
    electronics = await get_products(1)
    print(json.dumps(electronics, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    # تشغيل الاختبار
    asyncio.run(test_api())
